const mongoose = require('mongoose');
const SystemSettings = require('../../model/systemSettings');
const NotificationTemplate = require('../../model/notificationTemplate');
const AuditLog = require('../../model/auditLog');

// Get system settings
exports.getSystemSettings = async (req, res) => {
    try {
        const settings = await SystemSettings.find().sort({ category: 1, key: 1 });
        
        // Group settings by category
        const groupedSettings = settings.reduce((acc, setting) => {
            if (!acc[setting.category]) {
                acc[setting.category] = [];
            }
            acc[setting.category].push({
                key: setting.key,
                value: setting.value,
                description: setting.description,
                isEditable: setting.isEditable
            });
            return acc;
        }, {});

        res.status(200).json({
            success: true,
            data: groupedSettings
        });
    } catch (error) {
        res.status(500).json({
            success: false,
            message: 'Failed to fetch system settings',
            error: error.message
        });
    }
};

// Update system settings
exports.updateSystemSettings = async (req, res) => {
    try {
        const { settings } = req.body;
        const adminId = req.user.id;

        if (!settings || !Array.isArray(settings)) {
            return res.status(400).json({
                success: false,
                message: 'Settings array is required'
            });
        }

        const updateResults = [];
        
        for (const setting of settings) {
            const { key, value } = setting;
            
            const updatedSetting = await SystemSettings.findOneAndUpdate(
                { key },
                { 
                    value, 
                    lastModifiedBy: adminId 
                },
                { new: true, upsert: true }
            );
            
            updateResults.push(updatedSetting);
        }

        // Log the action
        await AuditLog.create({
            adminId,
            action: 'system_settings_update',
            targetType: 'System',
            targetId: adminId,
            details: { updatedSettings: settings }
        });

        res.status(200).json({
            success: true,
            message: 'System settings updated successfully',
            data: updateResults
        });
    } catch (error) {
        res.status(500).json({
            success: false,
            message: 'Failed to update system settings',
            error: error.message
        });
    }
};

// Get commission settings
exports.getCommissionSettings = async (req, res) => {
    try {
        const commissionSettings = await SystemSettings.find({ 
            category: 'commission' 
        }).sort({ key: 1 });

        const settings = {
            brokerCommissionRate: 2.5,
            contractorFeeRate: 1.5,
            platformFeeRate: 1.0,
            minimumCommission: 1000,
            maximumCommission: 100000,
            paymentSchedule: 'monthly'
        };

        res.status(200).json({
            success: true,
            data: settings
        });
    } catch (error) {
        res.status(500).json({
            success: false,
            message: 'Failed to fetch commission settings',
            error: error.message
        });
    }
};

// Update commission settings
exports.updateCommissionSettings = async (req, res) => {
    try {
        const commissionSettings = req.body;
        const adminId = req.user.id;

        // Mock update - in real implementation, update individual settings
        const updateResult = {
            ...commissionSettings,
            updatedBy: adminId,
            updatedAt: new Date()
        };

        // Log the action
        await AuditLog.create({
            adminId,
            action: 'commission_settings_update',
            targetType: 'System',
            targetId: adminId,
            details: { newSettings: commissionSettings }
        });

        res.status(200).json({
            success: true,
            message: 'Commission settings updated successfully',
            data: updateResult
        });
    } catch (error) {
        res.status(500).json({
            success: false,
            message: 'Failed to update commission settings',
            error: error.message
        });
    }
};

// Send bulk notification
exports.sendBulkNotification = async (req, res) => {
    try {
        const { 
            title, 
            message, 
            type = 'in_app', 
            targetAudience, 
            filters = {},
            scheduleAt 
        } = req.body;
        const adminId = req.user.id;

        if (!title || !message || !targetAudience) {
            return res.status(400).json({
                success: false,
                message: 'Title, message, and target audience are required'
            });
        }

        // Mock bulk notification sending
        const notificationResult = {
            _id: new mongoose.Types.ObjectId(),
            title,
            message,
            type,
            targetAudience,
            filters,
            scheduleAt: scheduleAt ? new Date(scheduleAt) : new Date(),
            sentBy: adminId,
            status: scheduleAt ? 'scheduled' : 'sent',
            recipientCount: 1250, // Mock count
            deliveredCount: 0,
            createdAt: new Date()
        };

        // Log the action
        await AuditLog.create({
            adminId,
            action: 'notification_sent',
            targetType: 'System',
            targetId: adminId,
            details: { title, targetAudience, recipientCount: 1250 }
        });

        res.status(200).json({
            success: true,
            message: scheduleAt ? 'Notification scheduled successfully' : 'Notification sent successfully',
            data: notificationResult
        });
    } catch (error) {
        res.status(500).json({
            success: false,
            message: 'Failed to send bulk notification',
            error: error.message
        });
    }
};

// Get notification templates
exports.getNotificationTemplates = async (req, res) => {
    try {
        const templates = await NotificationTemplate.find({ isActive: true })
            .populate('createdBy', 'name email')
            .sort({ category: 1, name: 1 });

        res.status(200).json({
            success: true,
            data: templates
        });
    } catch (error) {
        res.status(500).json({
            success: false,
            message: 'Failed to fetch notification templates',
            error: error.message
        });
    }
};

// Create notification template
exports.createNotificationTemplate = async (req, res) => {
    try {
        const templateData = req.body;
        const adminId = req.user.id;

        const template = new NotificationTemplate({
            ...templateData,
            createdBy: adminId
        });

        await template.save();

        res.status(201).json({
            success: true,
            message: 'Notification template created successfully',
            data: template
        });
    } catch (error) {
        res.status(500).json({
            success: false,
            message: 'Failed to create notification template',
            error: error.message
        });
    }
};

// Update notification template
exports.updateNotificationTemplate = async (req, res) => {
    try {
        const { templateId } = req.params;
        const templateData = req.body;
        const adminId = req.user.id;

        const template = await NotificationTemplate.findByIdAndUpdate(
            templateId,
            {
                ...templateData,
                lastModifiedBy: adminId
            },
            { new: true }
        );

        if (!template) {
            return res.status(404).json({
                success: false,
                message: 'Template not found'
            });
        }

        res.status(200).json({
            success: true,
            message: 'Notification template updated successfully',
            data: template
        });
    } catch (error) {
        res.status(500).json({
            success: false,
            message: 'Failed to update notification template',
            error: error.message
        });
    }
};
