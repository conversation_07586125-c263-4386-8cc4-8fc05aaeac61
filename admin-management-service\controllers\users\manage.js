const mongoose = require('mongoose');
const ExpressError = require('@build-connect/utils/ExpressError');

// Note: These models are from other services, we'll need to connect to their databases
// For now, we'll create a service to make HTTP requests to other services

/**
 * Get all users with pagination and filtering
 */
exports.getAllUsers = async (req, res) => {
    const {
        page = 1,
        limit = 10,
        search = '',
        role = '',
        status = '',
        sortBy = 'createdAt',
        order = 'desc'
    } = req.query;

    try {
        // Make HTTP request to user-management-service
        const response = await fetch(`http://localhost:3007/user-service/api/v1/admin/users?page=${page}&limit=${limit}&search=${search}&role=${role}&status=${status}&sortBy=${sortBy}&order=${order}`, {
            method: 'GET',
            headers: {
                'Authorization': req.headers.authorization,
                'Session': req.headers.session,
                'Content-Type': 'application/json'
            }
        });

        if (!response.ok) {
            throw new ExpressError('Failed to fetch users from user service', response.status);
        }

        const data = await response.json();
        res.status(200).json(data);
    } catch (error) {
        // Fallback: Return mock data structure for now
        res.status(200).json({
            users: [],
            pagination: {
                currentPage: parseInt(page),
                totalPages: 0,
                totalCount: 0,
                limit: parseInt(limit),
                hasNextPage: false,
                hasPrevPage: false
            },
            message: 'User service temporarily unavailable'
        });
    }
};

/**
 * Get user by ID
 */
exports.getUserById = async (req, res) => {
    const { id } = req.params;

    if (!mongoose.Types.ObjectId.isValid(id)) {
        throw new ExpressError('Invalid user ID', 400);
    }

    try {
        const response = await fetch(`http://localhost:3007/user-service/api/v1/admin/users/${id}`, {
            method: 'GET',
            headers: {
                'Authorization': req.headers.authorization,
                'Session': req.headers.session,
                'Content-Type': 'application/json'
            }
        });

        if (!response.ok) {
            if (response.status === 404) {
                throw new ExpressError('User not found', 404);
            }
            throw new ExpressError('Failed to fetch user from user service', response.status);
        }

        const data = await response.json();
        res.status(200).json(data);
    } catch (error) {
        if (error instanceof ExpressError) {
            throw error;
        }
        throw new ExpressError('User service temporarily unavailable', 503);
    }
};

/**
 * Update user status (activate/deactivate)
 */
exports.updateUserStatus = async (req, res) => {
    const { id } = req.params;
    const { isActive, reason } = req.body;

    if (!mongoose.Types.ObjectId.isValid(id)) {
        throw new ExpressError('Invalid user ID', 400);
    }

    if (typeof isActive !== 'boolean') {
        throw new ExpressError('isActive must be a boolean value', 400);
    }

    try {
        const response = await fetch(`http://localhost:3007/user-service/api/v1/admin/users/${id}/status`, {
            method: 'PATCH',
            headers: {
                'Authorization': req.headers.authorization,
                'Session': req.headers.session,
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ isActive, reason, updatedBy: req.user.id })
        });

        if (!response.ok) {
            if (response.status === 404) {
                throw new ExpressError('User not found', 404);
            }
            throw new ExpressError('Failed to update user status', response.status);
        }

        const data = await response.json();
        res.status(200).json(data);
    } catch (error) {
        if (error instanceof ExpressError) {
            throw error;
        }
        throw new ExpressError('User service temporarily unavailable', 503);
    }
};

/**
 * Get user statistics
 */
exports.getUserStats = async (req, res) => {
    const { timeframe = '30d' } = req.query;

    try {
        const response = await fetch(`http://localhost:3007/user-service/api/v1/admin/stats?timeframe=${timeframe}`, {
            method: 'GET',
            headers: {
                'Authorization': req.headers.authorization,
                'Session': req.headers.session,
                'Content-Type': 'application/json'
            }
        });

        if (!response.ok) {
            throw new ExpressError('Failed to fetch user statistics', response.status);
        }

        const data = await response.json();
        res.status(200).json(data);
    } catch (error) {
        // Fallback: Return mock statistics
        res.status(200).json({
            timeframe,
            stats: {
                totalUsers: 0,
                activeUsers: 0,
                inactiveUsers: 0,
                newUsers: 0,
                contractors: 0,
                brokers: 0,
                regularUsers: 0,
                verifiedUsers: 0,
                unverifiedUsers: 0
            },
            message: 'User service temporarily unavailable'
        });
    }
};

/**
 * Get contractors with their verification status
 */
exports.getContractors = async (req, res) => {
    const {
        page = 1,
        limit = 10,
        search = '',
        verificationStatus = '',
        sortBy = 'createdAt',
        order = 'desc'
    } = req.query;

    try {
        const response = await fetch(`http://localhost:3007/user-service/api/v1/admin/contractors?page=${page}&limit=${limit}&search=${search}&verificationStatus=${verificationStatus}&sortBy=${sortBy}&order=${order}`, {
            method: 'GET',
            headers: {
                'Authorization': req.headers.authorization,
                'Session': req.headers.session,
                'Content-Type': 'application/json'
            }
        });

        if (!response.ok) {
            throw new ExpressError('Failed to fetch contractors', response.status);
        }

        const data = await response.json();
        res.status(200).json(data);
    } catch (error) {
        res.status(200).json({
            contractors: [],
            pagination: {
                currentPage: parseInt(page),
                totalPages: 0,
                totalCount: 0,
                limit: parseInt(limit),
                hasNextPage: false,
                hasPrevPage: false
            },
            message: 'User service temporarily unavailable'
        });
    }
};

/**
 * Get brokers with their verification status
 */
exports.getBrokers = async (req, res) => {
    const {
        page = 1,
        limit = 10,
        search = '',
        verificationStatus = '',
        sortBy = 'createdAt',
        order = 'desc'
    } = req.query;

    try {
        const response = await fetch(`http://localhost:3007/user-service/api/v1/admin/brokers?page=${page}&limit=${limit}&search=${search}&verificationStatus=${verificationStatus}&sortBy=${sortBy}&order=${order}`, {
            method: 'GET',
            headers: {
                'Authorization': req.headers.authorization,
                'Session': req.headers.session,
                'Content-Type': 'application/json'
            }
        });

        if (!response.ok) {
            throw new ExpressError('Failed to fetch brokers', response.status);
        }

        const data = await response.json();
        res.status(200).json(data);
    } catch (error) {
        res.status(200).json({
            brokers: [],
            pagination: {
                currentPage: parseInt(page),
                totalPages: 0,
                totalCount: 0,
                limit: parseInt(limit),
                hasNextPage: false,
                hasPrevPage: false
            },
            message: 'User service temporarily unavailable'
        });
    }
};
