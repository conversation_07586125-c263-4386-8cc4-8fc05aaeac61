const VerificationRequests = require('../../model/verificationRequests');

exports.getVerificationRequests = async (req, res) => {
    const {
        status = 'pending',
        type,
        sortBy = 'createdAt',
        order = 'desc',
        limit = 10,
        page = 1,
    } = req.query;

    const filter = {};

    // Filter by status
    const validStatuses = ['pending', 'rejected', 'approved'];
    if (status && validStatuses.includes(status)) {
        filter.status = status;
    }

    // Filter by type
    const validTypes = ['contractor', 'broker'];
    if (type && validTypes.includes(type)) {
        filter.type = type;
    }

    const pageNumber = Math.max(1, parseInt(page, 10) || 1);
    const limitNumber = Math.max(1, parseInt(limit, 10) || 10);
    const skip = (pageNumber - 1) * limitNumber;

    const sortOrder = order === 'asc' ? 1 : -1;
    const sortObject = { [sortBy]: sortOrder };

    const verificationRequests = await VerificationRequests.find(filter)
        .sort(sortObject)
        .skip(skip)
        .limit(limitNumber);

    const totalCount = await VerificationRequests.countDocuments(filter);
    const totalPages = Math.ceil(totalCount / limitNumber);

    res.status(200).json({
        verificationRequests,
        pagination: {
            currentPage: pageNumber,
            totalPages,
            totalCount,
            limit: limitNumber,
            hasNextPage: pageNumber < totalPages,
            hasPrevPage: pageNumber > 1,
        },
    });
};

exports.getVerificationRequest = async (req, res) => {
    const { id } = req.params;

    const verificationRequest = await VerificationRequests.findById(id);

    if (!verificationRequest) {
        res.status(404);
        throw new Error('Verification request not found', 404);
    }

    res.status(200).json({
        verificationRequest,
    });
};
