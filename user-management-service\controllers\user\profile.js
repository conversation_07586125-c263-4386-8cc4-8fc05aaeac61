const ExpressError = require('@build-connect/utils/ExpressError');
const User = require('../../model/user');
const Asset = require('../../model/Asset');
const { createAssetFromFile } = require('../assets');
const { Cloudinary } = require('../../cloudinary');

// Get user profile
exports.getUserProfile = async (req, res) => {
    const userId = req.user.id;

    const user = await User.findById(userId).select('-password');

    if (!user) {
        throw new ExpressError('User not found', 404);
    }

    const avatar = await Asset.findOne({
        entityId: user._id,
        assetType: 'avatar',
    });

    res.status(200).json({
        user: {
            id: user._id,
            name: user.name,
            email: user.email,
            phone: user.phone,
            avatar: avatar ? avatar.imageURL : null,
            role: user.role,
            location: user.location,
            verificationStatus: user.verificationStatus,
            isEmailVerified: user.isEmailVerified,
            isPhoneVerified: user.isPhoneVerified,
            isAvailable: user.isAvailable,
            createdAt: user.createdAt,
            updatedAt: user.updatedAt,
            partnershipRequest: user.partnershipRequest,
        },
    });
};

// Update user profile
exports.updateUserProfile = async (req, res) => {
    const userId = req.user.id;
    const { name, phone, location } = req.body;

    console.log('body: ', req.body, req.file);

    const user = await User.findById(userId);

    if (!user) {
        throw new ExpressError('User not found', 404);
    }

    const updateData = {};
    if (name) updateData.name = name;
    if (phone) updateData.phone = phone;
    if (location) updateData.location = location;

    try {
        await User.updateOne({ _id: userId }, updateData, {
            runValidators: true,
        });
    } catch (error) {
        if (error.code === 11000 && error.keyPattern?.phone) {
            throw new ExpressError('Phone number already exists', 409);
        }
        throw error;
    }

    console.log(req.file);

    // Handle avatar upload
    if (req.file && req.file.path) {
        // Delete old avatar asset if exists
        const oldAvatarAsset = await Asset.findOneAndDelete({
            entityId: userId,
            entityType: 'User',
            assetType: 'avatar',
        });

        if (oldAvatarAsset) {
            const { result: deleteResult } =
                await Cloudinary.getCloudinary().uploader.destroy(
                    oldAvatarAsset.fileName
                );

            if (deleteResult !== 'ok' && deleteResult !== 'not found') {
                throw new ExpressError(
                    'Failed to delete old avatar asset',
                    500
                );
            }
        }

        // Create new avatar asset
        await createAssetFromFile(req.file, userId, 'User', 'avatar');
    }

    res.status(200).json({
        message: 'User profile updated successfully',
    });
};
