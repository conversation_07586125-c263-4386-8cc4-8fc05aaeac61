const mongoose = require('mongoose');

const notificationTemplateSchema = new mongoose.Schema(
    {
        name: {
            type: String,
            required: true,
            unique: true,
        },
        subject: {
            type: String,
            required: true,
        },
        body: {
            type: String,
            required: true,
        },
        type: {
            type: String,
            enum: ['email', 'sms', 'push', 'in_app'],
            required: true,
        },
        category: {
            type: String,
            enum: [
                'verification',
                'approval',
                'rejection',
                'notification',
                'marketing',
                'system',
            ],
            required: true,
        },
        variables: {
            type: [String],
            default: [],
        },
        isActive: {
            type: Boolean,
            default: true,
        },
        createdBy: {
            type: mongoose.Schema.Types.ObjectId,
            ref: 'Admin',
            required: true,
        },
        lastModifiedBy: {
            type: mongoose.Schema.Types.ObjectId,
            ref: 'Admin',
        },
    },
    {
        timestamps: true,
    }
);

const NotificationTemplate = mongoose.model(
    'NotificationTemplate',
    notificationTemplateSchema
);
module.exports = NotificationTemplate;
