const mongoose = require('mongoose');
const AuditLog = require('../../model/auditLog');
const Ticket = require('../../model/ticket');

// Get all tickets with pagination and filters
exports.getAllTickets = async (req, res) => {
    try {
        const {
            page = 1,
            limit = 20,
            status,
            category,
            priority,
            assignedTo,
            search,
            sortBy = 'createdAt',
            order = 'desc'
        } = req.query;

        const skip = (parseInt(page) - 1) * parseInt(limit);
        const filter = {};

        // Apply filters
        if (status) filter.status = status;
        if (category) filter.category = category;
        if (priority) filter.priority = priority;
        if (assignedTo) filter.assignedTo = assignedTo;

        // Mock ticket data - replace with actual database query
        const mockTickets = {
            tickets: [
                {
                    _id: '507f1f77bcf86cd799439028',
                    ticketNumber: 'TKT-000001',
                    userId: '507f1f77bcf86cd799439011',
                    user: { name: '<PERSON>', email: '<EMAIL>' },
                    subject: 'Unable to upload documents',
                    category: 'technical',
                    priority: 'medium',
                    status: 'open',
                    assignedTo: null,
                    createdAt: '2024-01-22T09:15:00Z',
                    lastResponseAt: '2024-01-22T09:15:00Z'
                },
                {
                    _id: '507f1f77bcf86cd799439029',
                    ticketNumber: 'TKT-000002',
                    userId: '507f1f77bcf86cd799439012',
                    user: { name: 'Jane Smith', email: '<EMAIL>' },
                    subject: 'Commission payment delay',
                    category: 'billing',
                    priority: 'high',
                    status: 'in_progress',
                    assignedTo: '507f1f77bcf86cd799439030',
                    assignedAdmin: { name: 'Admin User', email: '<EMAIL>' },
                    createdAt: '2024-01-21T14:30:00Z',
                    lastResponseAt: '2024-01-22T08:45:00Z'
                }
            ],
            pagination: {
                currentPage: parseInt(page),
                totalPages: 4,
                totalTickets: 78,
                hasNext: true,
                hasPrev: false
            }
        };

        res.status(200).json({
            success: true,
            data: mockTickets.tickets,
            pagination: mockTickets.pagination
        });
    } catch (error) {
        res.status(500).json({
            success: false,
            message: 'Failed to fetch tickets',
            error: error.message
        });
    }
};

// Get ticket details
exports.getTicketDetails = async (req, res) => {
    try {
        const { ticketId } = req.params;

        // Mock ticket details
        const ticketDetails = {
            _id: ticketId,
            ticketNumber: 'TKT-000001',
            userId: '507f1f77bcf86cd799439011',
            user: {
                name: 'John Doe',
                email: '<EMAIL>',
                phone: '+91-9876543210'
            },
            subject: 'Unable to upload documents',
            description: 'I am trying to upload my property documents but the upload keeps failing. Please help.',
            category: 'technical',
            priority: 'medium',
            status: 'open',
            assignedTo: null,
            tags: ['upload', 'documents', 'technical'],
            attachments: [
                {
                    fileName: 'error_screenshot.png',
                    fileUrl: 'https://example.com/screenshot.png',
                    fileType: 'image/png'
                }
            ],
            responses: [
                {
                    _id: '507f1f77bcf86cd799439031',
                    message: 'I am trying to upload my property documents but the upload keeps failing. Please help.',
                    isInternal: false,
                    respondedBy: '507f1f77bcf86cd799439011',
                    respondedByModel: 'User',
                    createdAt: '2024-01-22T09:15:00Z'
                }
            ],
            createdAt: '2024-01-22T09:15:00Z',
            lastResponseAt: '2024-01-22T09:15:00Z'
        };

        res.status(200).json({
            success: true,
            data: ticketDetails
        });
    } catch (error) {
        res.status(500).json({
            success: false,
            message: 'Failed to fetch ticket details',
            error: error.message
        });
    }
};

// Update ticket status
exports.updateTicketStatus = async (req, res) => {
    try {
        const { ticketId } = req.params;
        const { status, response = '' } = req.body;
        const adminId = req.user.id;

        const validStatuses = ['open', 'in_progress', 'resolved', 'closed'];
        if (!validStatuses.includes(status)) {
            return res.status(400).json({
                success: false,
                message: 'Invalid status'
            });
        }

        // Mock status update
        const updateResult = {
            ticketId,
            status,
            updatedBy: adminId,
            updatedAt: new Date(),
            response
        };

        // Log the action
        await AuditLog.create({
            adminId,
            action: 'ticket_status_update',
            targetType: 'Ticket',
            targetId: ticketId,
            details: { newStatus: status, response }
        });

        res.status(200).json({
            success: true,
            message: `Ticket status updated to ${status}`,
            data: updateResult
        });
    } catch (error) {
        res.status(500).json({
            success: false,
            message: 'Failed to update ticket status',
            error: error.message
        });
    }
};

// Assign ticket to admin
exports.assignTicket = async (req, res) => {
    try {
        const { ticketId } = req.params;
        const { assigneeId } = req.body;
        const adminId = req.user.id;

        if (!assigneeId) {
            return res.status(400).json({
                success: false,
                message: 'Assignee ID is required'
            });
        }

        // Mock assignment
        const assignmentResult = {
            ticketId,
            assignedTo: assigneeId,
            assignedBy: adminId,
            assignedAt: new Date()
        };

        // Log the action
        await AuditLog.create({
            adminId,
            action: 'ticket_assignment',
            targetType: 'Ticket',
            targetId: ticketId,
            details: { assignedTo: assigneeId }
        });

        res.status(200).json({
            success: true,
            message: 'Ticket assigned successfully',
            data: assignmentResult
        });
    } catch (error) {
        res.status(500).json({
            success: false,
            message: 'Failed to assign ticket',
            error: error.message
        });
    }
};

// Add response to ticket
exports.addTicketResponse = async (req, res) => {
    try {
        const { ticketId } = req.params;
        const { response, isInternal = false } = req.body;
        const adminId = req.user.id;

        if (!response) {
            return res.status(400).json({
                success: false,
                message: 'Response message is required'
            });
        }

        // Mock response addition
        const responseResult = {
            ticketId,
            response: {
                _id: new mongoose.Types.ObjectId(),
                message: response,
                isInternal,
                respondedBy: adminId,
                respondedByModel: 'Admin',
                createdAt: new Date()
            }
        };

        res.status(200).json({
            success: true,
            message: 'Response added successfully',
            data: responseResult
        });
    } catch (error) {
        res.status(500).json({
            success: false,
            message: 'Failed to add response',
            error: error.message
        });
    }
};
