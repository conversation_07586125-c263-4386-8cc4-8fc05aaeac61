const bcrypt = require('bcrypt');
const crypto = require('crypto');
const {
    createJSONToken,
    sessionID,
    base64Encode,
    getDecodedToken,
} = require('@build-connect/utils');
const ExpressError = require('@build-connect/utils/ExpressError');
const { withTransaction } = require('@build-connect/utils/transaction');
const User = require('../../model/user');
const { createAssetFromFile } = require('../assets');
const { client } = require('../../cache');
const { JWT_REFRESH_KEY, JWT_ACCESS_KEY } = require('../../config').getAll();
const { sendPasswordResetEmail } = require('../../utils/email');

exports.signup = async (req, res) => {
    const { email, password, phone, name } = req.body;
    const hashedPw = await bcrypt.hash(password, 10);

    await withTransaction(async (session) => {
        const user = new User({
            email,
            password: hashedPw,
            phone,
            name,
        });

        await user.save({ session });

        if (req.file && req.file.path) {
            await createAssetFromFile(
                req.file,
                user._id,
                'User',
                'avatar',
                session
            );
        }

        return user;
    });

    res.status(200).json({
        message: 'Successfully signed up!',
    });
};

exports.login = async (req, res) => {
    const { email, password } = req.body;
    const user = await User.findOne({ email });

    if (!user) {
        throw new ExpressError('email or password is incorrect', 401);
    }

    const isMatch = await bcrypt.compare(password, user.password);
    if (!isMatch) {
        throw new ExpressError('email or password is incorrect', 401);
    }

    const accessToken = createJSONToken({}, JWT_ACCESS_KEY, '10h');

    const refreshToken = createJSONToken(
        { userId: user._id.toString() },
        JWT_REFRESH_KEY,
        '720h'
    );

    const id = sessionID();

    const idToken = {
        id: user._id.toString(),
        email: user.email,
        name: user.name,
        role: user.role,
        verificationStatus: user.verificationStatus,
        isEmailVerified: user.isEmailVerified,
        isPhoneVerified: user.isPhoneVerified,
        isAvailable: user.isAvailable,
        partnershipRequest: user.partnershipRequest,
    };

    const tokenResponse = {
        accessToken,
        refreshToken,
        idToken: base64Encode(JSON.stringify(idToken)),
    };

    await client.set(id, JSON.stringify(tokenResponse), { EX: 720 * 3600 });

    res.status(200).json({
        message: 'logged in sucessfully',
        accessToken,
        sessionId: id,
    });
};

exports.refreshToken = async (req, res) => {
    const id = req.get('Session');

    if (!id) {
        return res.status(401).json({ message: 'unauthorized' });
    }

    const tokenResponse = await client.get(id);

    if (!tokenResponse) {
        return res.status(401).json({ message: 'unauthorized' });
    }

    const parsedTokenResponse = JSON.parse(tokenResponse);
    getDecodedToken(parsedTokenResponse.refreshToken);

    const accessToken = createJSONToken({}, JWT_ACCESS_KEY, '10m');

    const token = {
        ...parsedTokenResponse,
        accessToken,
    };

    await client.set(id, JSON.stringify(token), {
        KEEPTTL: true,
    });

    res.status(200).json({ accessToken });
};

// Accepts: { email }
// Returns: 200 with message if code is sent
exports.forgotPassword = async (req, res) => {
    const { email } = req.body;

    const user = await User.findOne({ email });
    if (!user) {
        throw new ExpressError('Email not found', 404);
    }

    const emailKeySafe = email.toLowerCase();
    const RATE_LIMIT_KEY = `fp:rate:${emailKeySafe}`;
    const CODE_HASH_KEY = `fp:code:${emailKeySafe}`;

    const MAX_ATTEMPTS = 3;
    const WINDOW_SECONDS = 60 * 60; // 1 hour

    const attemptsStr = await client.get(RATE_LIMIT_KEY);
    let attempts = attemptsStr ? parseInt(attemptsStr, 10) : 0;
    if (attempts >= MAX_ATTEMPTS) {
        throw new ExpressError('Too many requests. Try again later.', 429);
    }

    function generateCode() {
        while (true) {
            const num = crypto.randomInt(0, 1000000);
            if (num >= 0 && num <= 999999) {
                return num.toString().padStart(6, '0');
            }
        }
    }
    const code = generateCode();

    const codeHash = crypto.createHash('sha256').update(code).digest('hex');

    const EXPIRE_MINUTES = 15;
    await client.set(CODE_HASH_KEY, codeHash, { EX: EXPIRE_MINUTES * 60 });

    attempts += 1;
    if (attempts === 1) {
        await client.set(RATE_LIMIT_KEY, String(attempts), {
            EX: WINDOW_SECONDS,
        });
    } else {
        await client.set(RATE_LIMIT_KEY, String(attempts), { KEEPTTL: true });
    }

    try {
        await sendPasswordResetEmail({
            to: user.email,
            name: user.name,
            code,
            expiresInMinutes: EXPIRE_MINUTES,
        });
    } catch (e) {
        await client.del(CODE_HASH_KEY);
        throw new ExpressError(
            'Failed to send verification code. Please try again.',
            502
        );
    }

    res.status(200).json({ message: 'Verification code sent to email' });
};

// Accepts: { email, code }
// Returns: 200 with reset token if code is valid
exports.verifyCode = async (req, res) => {
    const { email, code } = req.body;

    const user = await User.findOne({ email });
    if (!user) {
        throw new ExpressError('Email not found', 404);
    }

    const emailKeySafe = email.toLowerCase();
    const CODE_HASH_KEY = `fp:code:${emailKeySafe}`;

    const storedHash = await client.get(CODE_HASH_KEY);
    if (!storedHash) {
        throw new ExpressError('Invalid or expired verification code', 400);
    }

    const providedHash = crypto.createHash('sha256').update(code).digest('hex');
    if (providedHash !== storedHash) {
        throw new ExpressError('Invalid verification code', 400);
    }

    const token = sessionID();
    const tokenHash = crypto.createHash('sha256').update(token).digest('hex');
    const RESET_TOKEN_KEY = `fp:resettoken:${tokenHash}`;

    await client.set(RESET_TOKEN_KEY, emailKeySafe, { EX: 15 * 60 });
    await client.del(CODE_HASH_KEY);

    res.status(200).json({ message: 'Verification successful', token });
};

// Accepts: { token, newPassword, confirmPassword }
// Returns: 200 on success
exports.resetPassword = async (req, res) => {
    const { token, newPassword } = req.body;

    const tokenHash = crypto.createHash('sha256').update(token).digest('hex');
    const RESET_TOKEN_KEY = `fp:resettoken:${tokenHash}`;

    const emailKeySafe = await client.get(RESET_TOKEN_KEY);
    if (!emailKeySafe) {
        throw new ExpressError('Invalid or expired reset token', 400);
    }

    const user = await User.findOne({ email: emailKeySafe });
    if (!user) {
        await client.del(RESET_TOKEN_KEY);
        throw new ExpressError('User not found', 404);
    }

    const hashedPw = await bcrypt.hash(newPassword, 10);
    user.password = hashedPw;
    await user.save();

    await client.del(RESET_TOKEN_KEY);
    await client.del(`fp:code:${emailKeySafe}`);

    res.status(200).json({ message: 'Password has been reset successfully' });
};
