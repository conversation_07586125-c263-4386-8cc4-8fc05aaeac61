const mongoose = require('mongoose');

const reportSchema = new mongoose.Schema(
    {
        name: {
            type: String,
            required: true,
        },
        type: {
            type: String,
            enum: [
                'user_activity',
                'revenue',
                'transactions',
                'land_listings',
                'broker_performance',
                'contractor_performance',
            ],
            required: true,
        },
        parameters: {
            type: mongoose.Schema.Types.Mixed,
            default: {},
        },
        status: {
            type: String,
            enum: ['generating', 'completed', 'failed'],
            default: 'generating',
        },
        fileUrl: {
            type: String,
        },
        fileSize: {
            type: Number,
        },
        recordCount: {
            type: Number,
        },
        generatedBy: {
            type: mongoose.Schema.Types.ObjectId,
            ref: 'Admin',
            required: true,
        },
        completedAt: {
            type: Date,
        },
        expiresAt: {
            type: Date,
            default: () => new Date(Date.now() + 7 * 24 * 60 * 60 * 1000), // 7 days from now
        },
        error: {
            type: String,
        },
    },
    {
        timestamps: true,
    }
);

// Index for cleanup of expired reports
reportSchema.index({ expiresAt: 1 }, { expireAfterSeconds: 0 });

const Report = mongoose.model('Report', reportSchema);
module.exports = Report;
