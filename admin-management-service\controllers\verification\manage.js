const VerificationRequests = require('../../model/verificationRequests');
const { Kafka } = require('../../pubsub');
const { Topics } = require('@build-connect/utils/constants/topics');
const ExpressError = require('@build-connect/utils/ExpressError');

/**
 * Approve a verification request
 */
exports.approveVerificationRequest = async (req, res) => {
    const { id } = req.params;
    const { reasonForApproval, notes } = req.body;

    if (!req.user) {
        throw new ExpressError('User not authenticated', 401);
    }

    const adminId = req.user.id || req.user._id;

    const verificationRequest = await VerificationRequests.findById(id);

    if (!verificationRequest) {
        throw new ExpressError('Verification request not found', 404);
    }

    if (verificationRequest.status !== 'pending') {
        throw new ExpressError('Verification request has already been processed', 400);
    }

    // Update verification request
    verificationRequest.status = 'approved';
    verificationRequest.verifiedBy = adminId;
    verificationRequest.reasonForApproval = reasonForApproval || '';
    verificationRequest.notes = notes || '';
    verificationRequest.processedAt = new Date();

    await verificationRequest.save();

    // Publish approval event to update the respective service
    const approvalPayload = {
        verificationId: id,
        requesterId: verificationRequest.requesterId,
        type: verificationRequest.type,
        status: 'approved',
        verifiedBy: adminId,
        processedAt: verificationRequest.processedAt,
        reasonForApproval: reasonForApproval || '',
        notes: notes || ''
    };

    await Kafka.publish(Topics.VERIFICATION_APPROVED, approvalPayload);

    res.status(200).json({
        message: 'Verification request approved successfully',
        verificationRequest: {
            id: verificationRequest._id,
            type: verificationRequest.type,
            status: verificationRequest.status,
            processedAt: verificationRequest.processedAt,
            verifiedBy: adminId
        }
    });
};

/**
 * Reject a verification request
 */
exports.rejectVerificationRequest = async (req, res) => {
    const { id } = req.params;
    const { reasonForRejection, notes } = req.body;

    if (!req.user) {
        throw new ExpressError('User not authenticated', 401);
    }

    const adminId = req.user.id || req.user._id;

    if (!reasonForRejection || reasonForRejection.trim().length === 0) {
        throw new ExpressError('Reason for rejection is required', 400);
    }

    const verificationRequest = await VerificationRequests.findById(id);

    if (!verificationRequest) {
        throw new ExpressError('Verification request not found', 404);
    }

    if (verificationRequest.status !== 'pending') {
        throw new ExpressError('Verification request has already been processed', 400);
    }

    // Update verification request
    verificationRequest.status = 'rejected';
    verificationRequest.verifiedBy = adminId;
    verificationRequest.reasonForRejection = reasonForRejection;
    verificationRequest.notes = notes || '';
    verificationRequest.processedAt = new Date();

    await verificationRequest.save();

    // Publish rejection event to update the respective service
    const rejectionPayload = {
        verificationId: id,
        requesterId: verificationRequest.requesterId,
        type: verificationRequest.type,
        status: 'rejected',
        verifiedBy: adminId,
        processedAt: verificationRequest.processedAt,
        reasonForRejection: reasonForRejection,
        notes: notes || ''
    };

    await Kafka.publish(Topics.VERIFICATION_REJECTED, rejectionPayload);

    res.status(200).json({
        message: 'Verification request rejected successfully',
        verificationRequest: {
            id: verificationRequest._id,
            type: verificationRequest.type,
            status: verificationRequest.status,
            processedAt: verificationRequest.processedAt,
            verifiedBy: adminId,
            reasonForRejection: reasonForRejection
        }
    });
};

/**
 * Update verification request priority
 */
exports.updateVerificationPriority = async (req, res) => {
    const { id } = req.params;
    const { priority } = req.body;

    if (!['low', 'medium', 'high'].includes(priority)) {
        throw new ExpressError('Invalid priority. Must be low, medium, or high', 400);
    }

    const verificationRequest = await VerificationRequests.findById(id);

    if (!verificationRequest) {
        throw new ExpressError('Verification request not found', 404);
    }

    verificationRequest.priority = priority;
    await verificationRequest.save();

    res.status(200).json({
        message: 'Verification request priority updated successfully',
        verificationRequest: {
            id: verificationRequest._id,
            priority: verificationRequest.priority
        }
    });
};

/**
 * Add notes to verification request
 */
exports.addVerificationNotes = async (req, res) => {
    const { id } = req.params;
    const { notes } = req.body;

    if (!notes || notes.trim().length === 0) {
        throw new ExpressError('Notes cannot be empty', 400);
    }

    const verificationRequest = await VerificationRequests.findById(id);

    if (!verificationRequest) {
        throw new ExpressError('Verification request not found', 404);
    }

    verificationRequest.notes = notes;
    await verificationRequest.save();

    res.status(200).json({
        message: 'Notes added successfully',
        verificationRequest: {
            id: verificationRequest._id,
            notes: verificationRequest.notes
        }
    });
};

/**
 * Get verification statistics
 */
exports.getVerificationStats = async (req, res) => {
    const { timeframe = '30d' } = req.query;
    
    let dateFilter = {};
    const now = new Date();
    
    switch (timeframe) {
        case '7d':
            dateFilter = { createdAt: { $gte: new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000) } };
            break;
        case '30d':
            dateFilter = { createdAt: { $gte: new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000) } };
            break;
        case '90d':
            dateFilter = { createdAt: { $gte: new Date(now.getTime() - 90 * 24 * 60 * 60 * 1000) } };
            break;
        default:
            dateFilter = {};
    }

    const stats = await VerificationRequests.aggregate([
        { $match: dateFilter },
        {
            $group: {
                _id: null,
                total: { $sum: 1 },
                pending: { $sum: { $cond: [{ $eq: ['$status', 'pending'] }, 1, 0] } },
                approved: { $sum: { $cond: [{ $eq: ['$status', 'approved'] }, 1, 0] } },
                rejected: { $sum: { $cond: [{ $eq: ['$status', 'rejected'] }, 1, 0] } },
                contractors: { $sum: { $cond: [{ $eq: ['$type', 'contractor'] }, 1, 0] } },
                brokers: { $sum: { $cond: [{ $eq: ['$type', 'broker'] }, 1, 0] } },
                sites: { $sum: { $cond: [{ $eq: ['$type', 'site'] }, 1, 0] } },
                highPriority: { $sum: { $cond: [{ $eq: ['$priority', 'high'] }, 1, 0] } },
                mediumPriority: { $sum: { $cond: [{ $eq: ['$priority', 'medium'] }, 1, 0] } },
                lowPriority: { $sum: { $cond: [{ $eq: ['$priority', 'low'] }, 1, 0] } }
            }
        }
    ]);

    const result = stats[0] || {
        total: 0,
        pending: 0,
        approved: 0,
        rejected: 0,
        contractors: 0,
        brokers: 0,
        sites: 0,
        highPriority: 0,
        mediumPriority: 0,
        lowPriority: 0
    };

    // Calculate approval rate
    const processedCount = result.approved + result.rejected;
    const approvalRate = processedCount > 0 ? ((result.approved / processedCount) * 100).toFixed(2) : 0;

    res.status(200).json({
        timeframe,
        stats: {
            ...result,
            approvalRate: parseFloat(approvalRate),
            processedCount
        }
    });
};
