const ExpressError = require('@build-connect/utils/ExpressError');

/**
 * Get all transactions with pagination and filtering
 */
exports.getTransactions = async (req, res) => {
    const {
        page = 1,
        limit = 10,
        status = '',
        userId = '',
        sortBy = 'createdAt',
        order = 'desc'
    } = req.query;

    try {
        const response = await fetch(`http://localhost:3002/payment-service/api/v1/admin/transactions?page=${page}&limit=${limit}&status=${status}&userId=${userId}&sortBy=${sortBy}&order=${order}`, {
            method: 'GET',
            headers: {
                'Authorization': req.headers.authorization,
                'Session': req.headers.session,
                'Content-Type': 'application/json'
            }
        });

        if (!response.ok) {
            throw new ExpressError('Failed to fetch transactions', response.status);
        }

        const data = await response.json();
        res.status(200).json(data);
    } catch (error) {
        res.status(200).json({
            transactions: [],
            pagination: {
                currentPage: parseInt(page),
                totalPages: 0,
                totalCount: 0,
                limit: parseInt(limit),
                hasNextPage: false,
                hasPrevPage: false
            },
            message: 'Payment service temporarily unavailable'
        });
    }
};

/**
 * Get transaction by ID
 */
exports.getTransactionById = async (req, res) => {
    const { id } = req.params;

    try {
        const response = await fetch(`http://localhost:3002/payment-service/api/v1/admin/transactions/${id}`, {
            method: 'GET',
            headers: {
                'Authorization': req.headers.authorization,
                'Session': req.headers.session,
                'Content-Type': 'application/json'
            }
        });

        if (!response.ok) {
            if (response.status === 404) {
                throw new ExpressError('Transaction not found', 404);
            }
            throw new ExpressError('Failed to fetch transaction', response.status);
        }

        const data = await response.json();
        res.status(200).json(data);
    } catch (error) {
        if (error instanceof ExpressError) {
            throw error;
        }
        throw new ExpressError('Payment service temporarily unavailable', 503);
    }
};

/**
 * Get all ratings with pagination and filtering
 */
exports.getRatings = async (req, res) => {
    const {
        page = 1,
        limit = 10,
        rating = '',
        entityType = '',
        sortBy = 'createdAt',
        order = 'desc'
    } = req.query;

    try {
        const response = await fetch(`http://localhost:3003/rating-service/api/v1/admin/ratings?page=${page}&limit=${limit}&rating=${rating}&entityType=${entityType}&sortBy=${sortBy}&order=${order}`, {
            method: 'GET',
            headers: {
                'Authorization': req.headers.authorization,
                'Session': req.headers.session,
                'Content-Type': 'application/json'
            }
        });

        if (!response.ok) {
            throw new ExpressError('Failed to fetch ratings', response.status);
        }

        const data = await response.json();
        res.status(200).json(data);
    } catch (error) {
        res.status(200).json({
            ratings: [],
            pagination: {
                currentPage: parseInt(page),
                totalPages: 0,
                totalCount: 0,
                limit: parseInt(limit),
                hasNextPage: false,
                hasPrevPage: false
            },
            message: 'Rating service temporarily unavailable'
        });
    }
};

/**
 * Delete inappropriate rating
 */
exports.deleteRating = async (req, res) => {
    const { id } = req.params;
    const { reason } = req.body;

    if (!reason || reason.trim().length === 0) {
        throw new ExpressError('Reason for deletion is required', 400);
    }

    try {
        const response = await fetch(`http://localhost:3003/rating-service/api/v1/admin/ratings/${id}`, {
            method: 'DELETE',
            headers: {
                'Authorization': req.headers.authorization,
                'Session': req.headers.session,
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ reason, deletedBy: req.user.id })
        });

        if (!response.ok) {
            if (response.status === 404) {
                throw new ExpressError('Rating not found', 404);
            }
            throw new ExpressError('Failed to delete rating', response.status);
        }

        const data = await response.json();
        res.status(200).json(data);
    } catch (error) {
        if (error instanceof ExpressError) {
            throw error;
        }
        throw new ExpressError('Rating service temporarily unavailable', 503);
    }
};

/**
 * Get all notifications with pagination and filtering
 */
exports.getNotifications = async (req, res) => {
    const {
        page = 1,
        limit = 10,
        status = '',
        type = '',
        sortBy = 'createdAt',
        order = 'desc'
    } = req.query;

    try {
        const response = await fetch(`http://localhost:3004/notification-service/api/v1/admin/notifications?page=${page}&limit=${limit}&status=${status}&type=${type}&sortBy=${sortBy}&order=${order}`, {
            method: 'GET',
            headers: {
                'Authorization': req.headers.authorization,
                'Session': req.headers.session,
                'Content-Type': 'application/json'
            }
        });

        if (!response.ok) {
            throw new ExpressError('Failed to fetch notifications', response.status);
        }

        const data = await response.json();
        res.status(200).json(data);
    } catch (error) {
        res.status(200).json({
            notifications: [],
            pagination: {
                currentPage: parseInt(page),
                totalPages: 0,
                totalCount: 0,
                limit: parseInt(limit),
                hasNextPage: false,
                hasPrevPage: false
            },
            message: 'Notification service temporarily unavailable'
        });
    }
};

/**
 * Send broadcast notification
 */
exports.sendBroadcastNotification = async (req, res) => {
    const { title, message, type, targetAudience } = req.body;

    if (!title || !message) {
        throw new ExpressError('Title and message are required', 400);
    }

    try {
        const response = await fetch(`http://localhost:3004/notification-service/api/v1/admin/broadcast`, {
            method: 'POST',
            headers: {
                'Authorization': req.headers.authorization,
                'Session': req.headers.session,
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                title,
                message,
                type: type || 'general',
                targetAudience: targetAudience || 'all',
                sentBy: req.user.id
            })
        });

        if (!response.ok) {
            throw new ExpressError('Failed to send broadcast notification', response.status);
        }

        const data = await response.json();
        res.status(200).json(data);
    } catch (error) {
        if (error instanceof ExpressError) {
            throw error;
        }
        throw new ExpressError('Notification service temporarily unavailable', 503);
    }
};

/**
 * Get all support tickets with pagination and filtering
 */
exports.getSupportTickets = async (req, res) => {
    const {
        page = 1,
        limit = 10,
        status = '',
        priority = '',
        category = '',
        sortBy = 'createdAt',
        order = 'desc'
    } = req.query;

    try {
        const response = await fetch(`http://localhost:3005/customer-support-service/api/v1/admin/tickets?page=${page}&limit=${limit}&status=${status}&priority=${priority}&category=${category}&sortBy=${sortBy}&order=${order}`, {
            method: 'GET',
            headers: {
                'Authorization': req.headers.authorization,
                'Session': req.headers.session,
                'Content-Type': 'application/json'
            }
        });

        if (!response.ok) {
            throw new ExpressError('Failed to fetch support tickets', response.status);
        }

        const data = await response.json();
        res.status(200).json(data);
    } catch (error) {
        res.status(200).json({
            tickets: [],
            pagination: {
                currentPage: parseInt(page),
                totalPages: 0,
                totalCount: 0,
                limit: parseInt(limit),
                hasNextPage: false,
                hasPrevPage: false
            },
            message: 'Customer support service temporarily unavailable'
        });
    }
};

/**
 * Update support ticket status
 */
exports.updateSupportTicketStatus = async (req, res) => {
    const { id } = req.params;
    const { status, assignedTo, notes } = req.body;

    if (!['open', 'in_progress', 'resolved', 'closed'].includes(status)) {
        throw new ExpressError('Invalid status', 400);
    }

    try {
        const response = await fetch(`http://localhost:3005/customer-support-service/api/v1/admin/tickets/${id}/status`, {
            method: 'PATCH',
            headers: {
                'Authorization': req.headers.authorization,
                'Session': req.headers.session,
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                status,
                assignedTo: assignedTo || null,
                notes: notes || '',
                updatedBy: req.user.id
            })
        });

        if (!response.ok) {
            if (response.status === 404) {
                throw new ExpressError('Support ticket not found', 404);
            }
            throw new ExpressError('Failed to update support ticket', response.status);
        }

        const data = await response.json();
        res.status(200).json(data);
    } catch (error) {
        if (error instanceof ExpressError) {
            throw error;
        }
        throw new ExpressError('Customer support service temporarily unavailable', 503);
    }
};
