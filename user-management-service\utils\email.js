const log = require('@build-connect/utils/log');
let transporter = null;

function getSmtpTransport() {
    if (transporter) return transporter;

    const {
        SMTP_HOST,
        SMTP_PORT,
        SMTP_SECURE,
        SMTP_USER,
        SMTP_PASS,
        EMAIL_FROM,
    } = process.env;

    if (!SMTP_HOST || !SMTP_PORT || !SMTP_USER || !SMTP_PASS || !EMAIL_FROM) {
        log(
            'warn',
            'SMTP not fully configured. Falling back to log-only emails.',
            {
                missing: {
                    SMTP_HOST: !!SMTP_HOST,
                    SMTP_PORT: !!SMTP_PORT,
                    SMTP_USER: !!SMTP_USER,
                    SMTP_PASS: !!SMTP_PASS,
                    EMAIL_FROM: !!EMAIL_FROM,
                },
            }
        );
        return null;
    }

    let nodemailer;
    try {
        nodemailer = require('nodemailer');
    } catch (e) {
        log(
            'error',
            'Nodemailer is not installed. Please install nodemailer to enable SMTP email sending.',
            e
        );
        return null;
    }

    transporter = nodemailer.createTransport({
        host: SMTP_HOST,
        port: Number(SMTP_PORT),
        secure:
            String(SMTP_SECURE).toLowerCase() === 'true' ||
            Number(SMTP_PORT) === 465,
        auth: { user: SMTP_USER, pass: SMTP_PASS },
    });

    return transporter;
}

async function verifySmtp() {
    const smtp = getSmtpTransport();
    if (!smtp) {
        const { SMTP_HOST, SMTP_PORT, SMTP_USER, EMAIL_FROM } = process.env;
        const err = new Error('SMTP not configured');
        err.code = 'SMTP_NOT_CONFIGURED';
        err.details = {
            SMTP_HOST: SMTP_HOST || null,
            SMTP_PORT: SMTP_PORT || null,
            SMTP_USER: SMTP_USER || null,
            EMAIL_FROM: EMAIL_FROM || null,
            configured: false,
        };
        throw err;
    }
    return smtp.verify();
}

/**
 * sendPasswordResetEmail via SMTP (fallback to log-only if SMTP not configured)
 * @param {Object} params
 * @param {string} params.to - Recipient email address
 * @param {string} [params.name] - Recipient name
 * @param {string} params.code - 6-digit verification code
 * @param {number} params.expiresInMinutes - Expiration time in minutes
 */
async function sendPasswordResetEmail({ to, name, code, expiresInMinutes }) {
    const subject = 'Reset your Build Connect password';
    const greeting = name ? `Hi ${name},` : 'Hi,';
    const text = [
        greeting,
        '',
        'We received a request to reset your Build Connect password.',
        `Your verification code is: ${code}`,
        '',
        `This code will expire in ${expiresInMinutes} minutes.`,
        '',
        'If you did not request a password reset, you can safely ignore this email.',
        '',
        'Thanks,',
        'Build Connect Team',
    ].join('\n');

    const html = `
        <div style="font-family:Arial,Helvetica,sans-serif;max-width:560px;margin:0 auto;color:#111">
            <h2 style="color:#111;margin-bottom:8px">Password reset request</h2>
            <p style="margin:0 0 16px 0">${greeting}</p>
            <p style="margin:0 0 12px 0">We received a request to reset your Build Connect password.</p>
            <p style="margin:0 0 12px 0">Use the following verification code to continue:</p>
            <div style="margin:16px 0;padding:16px 24px;background:#f6f7f9;border-radius:8px;border:1px solid #e5e7eb;font-size:24px;letter-spacing:6px;text-align:center;font-weight:700;color:#111">
                ${code}
            </div>
            <p style="margin:0 0 12px 0">This code expires in <strong>${expiresInMinutes} minutes</strong>.</p>
            <p style="margin:0 0 12px 0">If you did not request this, you can safely ignore this email.</p>
            <p style="margin:24px 0 0 0">Thanks,<br/>Build Connect Team</p>
        </div>
    `;

    const smtp = getSmtpTransport();
    if (!smtp) {
        // Fallback: log only
        log('info', '[Password Reset Email - LOG ONLY]', {
            to,
            subject,
            textPreview: text.slice(0, 120) + '...',
            htmlPreview: html.replace(/\s+/g, ' ').slice(0, 160) + '...',
        });
        return true;
    }

    const { EMAIL_FROM } = process.env;

    try {
        await smtp.sendMail({ from: EMAIL_FROM, to, subject, text, html });
        return true;
    } catch (err) {
        // Surface error to caller for proper error handling/rollback
        throw err;
    }
}

module.exports = { sendPasswordResetEmail, verifySmtp };
