const { body, param, query } = require('express-validator');

// Common validation patterns
const REGEX_PATTERNS = {
    OBJECT_ID: /^[0-9a-fA-F]{24}$/,
    PRIORITY: /^(low|medium|high)$/,
    STATUS: /^(pending|approved|rejected)$/,
    VERIFICATION_TYPE: /^(contractor|broker|site)$/,
    USER_ROLE: /^(user|contractor|broker)$/,
    SORT_ORDER: /^(asc|desc)$/,
    TIMEFRAME: /^(7d|30d|90d|all)$/
};

// Reusable field validators
const validators = {
    // MongoDB ObjectId validation
    objectId: (field = 'id') =>
        param(field)
            .matches(REGEX_PATTERNS.OBJECT_ID)
            .withMessage(`${field} must be a valid MongoDB ObjectId`),

    // Pagination validators
    page: () =>
        query('page')
            .optional()
            .isInt({ min: 1 })
            .withMessage('Page must be a positive integer')
            .toInt(),

    limit: () =>
        query('limit')
            .optional()
            .isInt({ min: 1, max: 100 })
            .withMessage('Limit must be between 1 and 100')
            .toInt(),

    // Search and filtering
    search: () =>
        query('search')
            .optional()
            .trim()
            .isLength({ max: 100 })
            .withMessage('Search term cannot exceed 100 characters')
            .escape(),

    sortBy: (allowedFields = ['createdAt', 'updatedAt', 'name']) =>
        query('sortBy')
            .optional()
            .isIn(allowedFields)
            .withMessage(`sortBy must be one of: ${allowedFields.join(', ')}`),

    order: () =>
        query('order')
            .optional()
            .matches(REGEX_PATTERNS.SORT_ORDER)
            .withMessage('Order must be asc or desc'),

    timeframe: () =>
        query('timeframe')
            .optional()
            .matches(REGEX_PATTERNS.TIMEFRAME)
            .withMessage('Timeframe must be 7d, 30d, 90d, or all'),

    // Verification specific validators
    verificationStatus: () =>
        query('status')
            .optional()
            .matches(REGEX_PATTERNS.STATUS)
            .withMessage('Status must be pending, approved, or rejected'),

    verificationType: () =>
        query('type')
            .optional()
            .matches(REGEX_PATTERNS.VERIFICATION_TYPE)
            .withMessage('Type must be contractor, broker, or site'),

    priority: () =>
        body('priority')
            .matches(REGEX_PATTERNS.PRIORITY)
            .withMessage('Priority must be low, medium, or high'),

    reasonForRejection: () =>
        body('reasonForRejection')
            .trim()
            .notEmpty()
            .withMessage('Reason for rejection is required')
            .isLength({ min: 10, max: 500 })
            .withMessage('Reason must be between 10 and 500 characters')
            .escape(),

    reasonForApproval: () =>
        body('reasonForApproval')
            .optional()
            .trim()
            .isLength({ max: 500 })
            .withMessage('Reason for approval cannot exceed 500 characters')
            .escape(),

    notes: () =>
        body('notes')
            .trim()
            .notEmpty()
            .withMessage('Notes cannot be empty')
            .isLength({ min: 5, max: 1000 })
            .withMessage('Notes must be between 5 and 1000 characters')
            .escape(),

    // User management validators
    userRole: () =>
        query('role')
            .optional()
            .matches(REGEX_PATTERNS.USER_ROLE)
            .withMessage('Role must be user, contractor, or broker'),

    isActive: () =>
        body('isActive')
            .isBoolean()
            .withMessage('isActive must be a boolean value'),

    reason: () =>
        body('reason')
            .optional()
            .trim()
            .isLength({ max: 500 })
            .withMessage('Reason cannot exceed 500 characters')
            .escape(),

    // Site management validators
    siteStatus: () =>
        body('status')
            .isIn(['pending', 'approved', 'rejected'])
            .withMessage('Status must be pending, approved, or rejected'),

    state: () =>
        query('state')
            .optional()
            .trim()
            .isLength({ min: 2, max: 50 })
            .withMessage('State must be between 2 and 50 characters')
            .escape(),

    district: () =>
        query('district')
            .optional()
            .trim()
            .isLength({ min: 2, max: 50 })
            .withMessage('District must be between 2 and 50 characters')
            .escape(),

    // Notification validators
    title: () =>
        body('title')
            .trim()
            .notEmpty()
            .withMessage('Title is required')
            .isLength({ min: 5, max: 100 })
            .withMessage('Title must be between 5 and 100 characters')
            .escape(),

    message: () =>
        body('message')
            .trim()
            .notEmpty()
            .withMessage('Message is required')
            .isLength({ min: 10, max: 500 })
            .withMessage('Message must be between 10 and 500 characters')
            .escape(),

    notificationType: () =>
        body('type')
            .optional()
            .isIn(['general', 'urgent', 'maintenance', 'promotion'])
            .withMessage('Type must be general, urgent, maintenance, or promotion'),

    targetAudience: () =>
        body('targetAudience')
            .optional()
            .isIn(['all', 'users', 'contractors', 'brokers'])
            .withMessage('Target audience must be all, users, contractors, or brokers'),

    // Support ticket validators
    ticketStatus: () =>
        body('status')
            .isIn(['open', 'in_progress', 'resolved', 'closed'])
            .withMessage('Status must be open, in_progress, resolved, or closed'),

    assignedTo: () =>
        body('assignedTo')
            .optional()
            .matches(REGEX_PATTERNS.OBJECT_ID)
            .withMessage('Assigned to must be a valid user ID'),

    ticketPriority: () =>
        query('priority')
            .optional()
            .isIn(['low', 'medium', 'high', 'urgent'])
            .withMessage('Priority must be low, medium, high, or urgent'),

    category: () =>
        query('category')
            .optional()
            .isIn(['technical', 'billing', 'general', 'feature_request'])
            .withMessage('Category must be technical, billing, general, or feature_request')
};

// Composite validators for specific endpoints
const verificationListValidators = [
    validators.page(),
    validators.limit(),
    validators.search(),
    validators.verificationStatus(),
    validators.verificationType(),
    validators.sortBy(['createdAt', 'updatedAt', 'priority']),
    validators.order(),
    validators.timeframe()
];

const verificationApprovalValidators = [
    validators.objectId(),
    validators.reasonForApproval(),
    validators.notes()
];

const verificationRejectionValidators = [
    validators.objectId(),
    validators.reasonForRejection(),
    validators.notes()
];

const verificationPriorityValidators = [
    validators.objectId(),
    validators.priority()
];

const verificationNotesValidators = [
    validators.objectId(),
    validators.notes()
];

const userListValidators = [
    validators.page(),
    validators.limit(),
    validators.search(),
    validators.userRole(),
    validators.sortBy(['createdAt', 'updatedAt', 'name', 'email']),
    validators.order()
];

const userStatusValidators = [
    validators.objectId(),
    validators.isActive(),
    validators.reason()
];

const siteListValidators = [
    validators.page(),
    validators.limit(),
    validators.search(),
    validators.state(),
    validators.district(),
    validators.sortBy(['createdAt', 'updatedAt', 'name', 'price']),
    validators.order()
];

const siteStatusValidators = [
    validators.objectId(),
    validators.siteStatus(),
    validators.reason()
];

const broadcastNotificationValidators = [
    validators.title(),
    validators.message(),
    validators.notificationType(),
    validators.targetAudience()
];

const supportTicketStatusValidators = [
    validators.objectId(),
    validators.ticketStatus(),
    validators.assignedTo(),
    validators.notes()
];

module.exports = {
    validators,
    verificationListValidators,
    verificationApprovalValidators,
    verificationRejectionValidators,
    verificationPriorityValidators,
    verificationNotesValidators,
    userListValidators,
    userStatusValidators,
    siteListValidators,
    siteStatusValidators,
    broadcastNotificationValidators,
    supportTicketStatusValidators
};
