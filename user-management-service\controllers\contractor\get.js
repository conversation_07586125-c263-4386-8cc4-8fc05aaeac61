const ExpressError = require('@build-connect/utils/ExpressError');
const mongoose = require('mongoose');
const Contractor = require('../../model/Contractor');
const Aadhaar = require('../../model/Aadhaar');
const PAN = require('../../model/Pan');
const Asset = require('../../model/Asset');
const User = require('../../model/user');

exports.getContractorApplicationByUserId = async (req, res) => {
    const userId = req.user.id;

    const contractorProfile = await Contractor.findOne({
        user: userId,
    }).lean();
    if (!contractorProfile) {
        throw new ExpressError('Contractor application not found', 404);
    }

    const aadhaar = await Aadhaar.findOne({ userId }).lean();
    const pan = await PAN.findOne({ userId }).lean();

    const aadhaarAsset = aadhaar
        ? await Asset.findOne({
              entityId: aadhaar._id,
              entityType: 'Aadhaar',
          }).lean()
        : null;
    const panAsset = pan
        ? await Asset.findOne({
              entityId: pan._id,
              entityType: 'PAN',
          }).lean()
        : null;

    res.status(200).json({
        application: {
            contractorId: contractorProfile._id,
            specialties: contractorProfile.specialties,
            portfolio: contractorProfile.portfolio,
            experience: contractorProfile.experience,
            serviceAreas: contractorProfile.serviceAreas,
            aadhaarNumber: aadhaar.aadhaarNumber,
            nameOnAadhaar: aadhaar.nameOnAadhaar,
            dateOfBirth: aadhaar.dateOfBirth,
            gender: aadhaar.gender,
            address: aadhaar.address,
            aadhaarAsset: aadhaarAsset ? aadhaarAsset.imageURL : null,
            panNumber: pan.panNumber,
            panName: pan.panName,
            panDateOfBirth: pan.dateOfBirth,
            panAsset: panAsset ? panAsset.imageURL : null,
            partnershipRequest: req.user.partnershipRequest,
        },
    });
};

exports.getAllContractors = async (req, res) => {
    try {
        // Get all verified contractors with their user information
        const contractors = await Contractor.find({
            verificationStatus: 'verified',
        })
            .populate('user', 'name isAvailable')
            .lean();

        // Get avatar assets for each contractor
        const contractorsWithAssets = await Promise.all(
            contractors.map(async (contractor) => {
                const avatarAsset = await Asset.findOne({
                    entityId: contractor.user._id,
                    entityType: 'User',
                    assetType: 'avatar',
                }).lean();

                return {
                    id: contractor.user._id, // Use user ID instead of contractor profile ID
                    profileId: contractor._id, // Keep profile ID for reference if needed
                    name: contractor.user.name,
                    serviceAreas: contractor.serviceAreas,
                    ratings: contractor.ratings,
                    image: avatarAsset ? avatarAsset.imageURL : null,
                    isAvailable: contractor.user.isAvailable,
                    specialties: contractor.specialties,
                    experience: contractor.experience,
                };
            })
        );

        res.status(200).json({
            success: true,
            contractors: contractorsWithAssets,
        });
    } catch (error) {
        throw new ExpressError('Failed to fetch contractors', 500);
    }
};

exports.getContractorProfile = async (req, res) => {
    const { contractorId } = req.params;

    try {
            contractor = await Contractor.findOne({ user: contractorId })
                .populate('user', 'name isAvailable')
                .lean();

        if (!contractor) {
            throw new ExpressError(`Contractor not found with ID: ${contractorId}`, 404);
        }

        // Get avatar asset for the contractor
        const avatarAsset = await Asset.findOne({
            entityId: contractor.user._id,
            entityType: 'User',
            assetType: 'avatar',
        }).lean();

        res.status(200).json({
            success: true,
            contractor: {
                id: contractor.user._id, // Use user ID instead of contractor profile ID
                profileId: contractor._id, // Keep profile ID for reference if needed
                name: contractor.user.name,
                isAvailable: contractor.user.isAvailable,
                experience: contractor.experience,
                serviceAreas: contractor.serviceAreas,
                specialties: contractor.specialties,
                ratings: contractor.ratings,
                image: avatarAsset ? avatarAsset.imageURL : null,
                portfolio: contractor.portfolio,
                verificationStatus: contractor.verificationStatus,
            },
        });
    } catch (error) {
        if (error instanceof ExpressError) {
            throw error;
        }
        throw new ExpressError('Failed to fetch contractor profile', 500);
    }
};
