const mongoose = require('mongoose');

const transactionSchema = new mongoose.Schema(
    {
        transactionId: {
            type: String,
            unique: true,
            required: true,
        },
        userId: {
            type: mongoose.Schema.Types.ObjectId,
            ref: 'User',
            required: true,
        },
        type: {
            type: String,
            enum: ['payment', 'refund', 'commission', 'penalty', 'bonus'],
            required: true,
        },
        category: {
            type: String,
            enum: [
                'site_purchase',
                'service_fee',
                'broker_commission',
                'contractor_payment',
                'platform_fee',
            ],
            required: true,
        },
        amount: {
            type: Number,
            required: true,
        },
        currency: {
            type: String,
            default: 'INR',
        },
        status: {
            type: String,
            enum: [
                'pending',
                'processing',
                'completed',
                'failed',
                'cancelled',
                'refunded',
                'disputed',
            ],
            default: 'pending',
        },
        paymentMethod: {
            type: String,
            enum: [
                'credit_card',
                'debit_card',
                'upi',
                'net_banking',
                'wallet',
                'bank_transfer',
            ],
        },
        paymentGateway: {
            type: String,
            enum: ['razorpay', 'stripe', 'payu', 'cashfree'],
        },
        gatewayTransactionId: {
            type: String,
        },
        relatedEntityType: {
            type: String,
            enum: ['Site', 'Project', 'Service'],
        },
        relatedEntityId: {
            type: mongoose.Schema.Types.ObjectId,
        },
        brokerId: {
            type: mongoose.Schema.Types.ObjectId,
            ref: 'User',
        },
        contractorId: {
            type: mongoose.Schema.Types.ObjectId,
            ref: 'User',
        },
        fees: {
            platformFee: { type: Number, default: 0 },
            brokerCommission: { type: Number, default: 0 },
            contractorFee: { type: Number, default: 0 },
            taxes: { type: Number, default: 0 },
        },
        refund: {
            amount: { type: Number, default: 0 },
            reason: String,
            processedBy: {
                type: mongoose.Schema.Types.ObjectId,
                ref: 'Admin',
            },
            processedAt: Date,
            refundTransactionId: String,
        },
        dispute: {
            reason: String,
            status: {
                type: String,
                enum: ['open', 'investigating', 'resolved', 'closed'],
            },
            raisedBy: {
                type: mongoose.Schema.Types.ObjectId,
                ref: 'User',
            },
            raisedAt: Date,
            assignedTo: {
                type: mongoose.Schema.Types.ObjectId,
                ref: 'Admin',
            },
            resolution: String,
            resolvedAt: Date,
        },
        metadata: {
            type: mongoose.Schema.Types.Mixed,
            default: {},
        },
        completedAt: Date,
        failureReason: String,
    },
    {
        timestamps: true,
    }
);

// Auto-generate transaction ID
transactionSchema.pre('save', async function (next) {
    if (this.isNew && !this.transactionId) {
        const count = await this.constructor.countDocuments();
        this.transactionId = `TXN-${Date.now()}-${String(count + 1).padStart(4, '0')}`;
    }
    next();
});

// Indexes for efficient querying
transactionSchema.index({ userId: 1, createdAt: -1 });
transactionSchema.index({ status: 1, createdAt: -1 });
transactionSchema.index({ type: 1, category: 1 });
transactionSchema.index({ transactionId: 1 });

const Transaction = mongoose.model('Transaction', transactionSchema);
module.exports = Transaction;
