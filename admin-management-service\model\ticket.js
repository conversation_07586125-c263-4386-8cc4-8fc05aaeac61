const mongoose = require('mongoose');

const ticketResponseSchema = new mongoose.Schema(
    {
        message: {
            type: String,
            required: true,
        },
        isInternal: {
            type: Boolean,
            default: false,
        },
        respondedBy: {
            type: mongoose.Schema.Types.ObjectId,
            refPath: 'respondedByModel',
            required: true,
        },
        respondedByModel: {
            type: String,
            enum: ['User', 'Admin'],
            required: true,
        },
        attachments: [
            {
                fileName: String,
                fileUrl: String,
                fileType: String,
            },
        ],
    },
    {
        timestamps: true,
    }
);

const ticketSchema = new mongoose.Schema(
    {
        ticketNumber: {
            type: String,
            unique: true,
            required: true,
        },
        userId: {
            type: mongoose.Schema.Types.ObjectId,
            ref: 'User',
            required: true,
        },
        subject: {
            type: String,
            required: true,
        },
        description: {
            type: String,
            required: true,
        },
        category: {
            type: String,
            enum: [
                'technical',
                'billing',
                'account',
                'verification',
                'general',
                'complaint',
            ],
            required: true,
        },
        priority: {
            type: String,
            enum: ['low', 'medium', 'high', 'urgent'],
            default: 'medium',
        },
        status: {
            type: String,
            enum: ['open', 'in_progress', 'resolved', 'closed'],
            default: 'open',
        },
        assignedTo: {
            type: mongoose.Schema.Types.ObjectId,
            ref: 'Admin',
        },
        responses: [ticketResponseSchema],
        tags: [String],
        attachments: [
            {
                fileName: String,
                fileUrl: String,
                fileType: String,
            },
        ],
        resolvedAt: {
            type: Date,
        },
        closedAt: {
            type: Date,
        },
        lastResponseAt: {
            type: Date,
        },
    },
    {
        timestamps: true,
    }
);

// Auto-generate ticket number
ticketSchema.pre('save', async function (next) {
    if (this.isNew && !this.ticketNumber) {
        const count = await this.constructor.countDocuments();
        this.ticketNumber = `TKT-${String(count + 1).padStart(6, '0')}`;
    }
    next();
});

// Update lastResponseAt when responses are added
ticketSchema.pre('save', function (next) {
    if (this.responses && this.responses.length > 0) {
        this.lastResponseAt =
            this.responses[this.responses.length - 1].createdAt || new Date();
    }
    next();
});

const Ticket = mongoose.model('Ticket', ticketSchema);
module.exports = Ticket;
