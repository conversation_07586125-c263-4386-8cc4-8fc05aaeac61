const mongoose = require('mongoose');

const verificationRequestsSchema = new mongoose.Schema({
    type: {
        type: String,
        enum: ['broker', 'contractor', 'site'],
        required: true,
    },
    requesterId: {
        type: String,
        required: true,
    },
    status: {
        type: String,
        enum: ['pending', 'approved', 'rejected'],
        default: 'pending',
    },
    varifiedBy: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'Admin',
        default: null,
    },
    reasonForRejection: {
        type: String,
        default: '',
    },
});

const VerificationRequests = mongoose.model(
    'VerificationRequests',
    verificationRequestsSchema
);
module.exports = VerificationRequests;
