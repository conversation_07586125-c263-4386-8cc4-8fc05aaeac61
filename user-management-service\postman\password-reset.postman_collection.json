{"info": {"name": "Build Connect - Password Reset Flow", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json", "_postman_id": "a1b2c3d4-e5f6-7890-1234-56789abcde00"}, "item": [{"name": "Email Test (SMTP Connectivity)", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{baseUrl}}/email-test", "host": ["{{baseUrl}}"], "path": ["email-test"]}, "body": {"mode": "raw", "raw": "{\n  \"to\": \"{{testEmail}}\"\n}"}}}, {"name": "Forgot Password", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{baseUrl}}/forgot-password", "host": ["{{baseUrl}}"], "path": ["forgot-password"]}, "body": {"mode": "raw", "raw": "{\n  \"email\": \"{{userEmail}}\"\n}"}}}, {"name": "Verify Code", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{baseUrl}}/verify-code", "host": ["{{baseUrl}}"], "path": ["verify-code"]}, "body": {"mode": "raw", "raw": "{\n  \"email\": \"{{userEmail}}\" ,\n  \"code\": \"{{verificationCode}}\"\n}"}}}, {"name": "Reset Password", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{baseUrl}}/reset-password", "host": ["{{baseUrl}}"], "path": ["reset-password"]}, "body": {"mode": "raw", "raw": "{\n  \"token\": \"{{resetToken}}\",\n  \"newPassword\": \"{{newPassword}}\",\n  \"confirmPassword\": \"{{newPassword}}\"\n}"}}}, {"name": "Login (Verify New Password)", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{baseUrl}}/login", "host": ["{{baseUrl}}"], "path": ["login"]}, "body": {"mode": "raw", "raw": "{\n  \"email\": \"{{userEmail}}\",\n  \"password\": \"{{newPassword}}\"\n}"}}}, {"name": "Forgot Password - Invalid Email Format (Error)", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{baseUrl}}/forgot-password", "host": ["{{baseUrl}}"], "path": ["forgot-password"]}, "body": {"mode": "raw", "raw": "{\n  \"email\": \"not-an-email\"\n}"}}}, {"name": "Forgot Password - Nonexistent User (Error)", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{baseUrl}}/forgot-password", "host": ["{{baseUrl}}"], "path": ["forgot-password"]}, "body": {"mode": "raw", "raw": "{\n  \"email\": \"noone+{{timestamp}}@example.com\"\n}"}}}, {"name": "Verify Code - Wrong Code (Error)", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{baseUrl}}/verify-code", "host": ["{{baseUrl}}"], "path": ["verify-code"]}, "body": {"mode": "raw", "raw": "{\n  \"email\": \"{{userEmail}}\",\n  \"code\": \"000000\"\n}"}}}, {"name": "Reset Password - <PERSON><PERSON><PERSON> (Error)", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{baseUrl}}/reset-password", "host": ["{{baseUrl}}"], "path": ["reset-password"]}, "body": {"mode": "raw", "raw": "{\n  \"token\": \"invalid-token\",\n  \"newPassword\": \"Abcdef1!\",\n  \"confirmPassword\": \"Abcdef1!\"\n}"}}}], "variable": [{"key": "timestamp", "value": "{{$timestamp}}"}]}