const express = require('express');

const router = express.Router();
const { catchAsync } = require('@build-connect/utils');
const createAuthMiddleware = require('@build-connect/utils/middleware/authmiddleware');
const {
    handleValidationErrors,
} = require('@build-connect/utils/middleware/expressValidator');
const multer = require('multer');
const { client } = require('../cache');
const authControllers = require('../controllers/user/auth');
const profileControllers = require('../controllers/user/profile');
const { createBrokerApplication } = require('../controllers/broker/create');
const { updateBrokerApplication } = require('../controllers/broker/update');
const {
    getBrokerApplicationByUserId,
    getAllBrokers,
    getBrokerProfile,
} = require('../controllers/broker/get');
const {
    createContractorApplication,
} = require('../controllers/contractor/create');
const {
    updateContractorApplication,
} = require('../controllers/contractor/update');
const {
    getContractorApplicationByUserId,
    getAllContractors,
    getContractorProfile,
} = require('../controllers/contractor/get');
const {
    getBrokers,
    getContractors,
} = require('../controllers/professionals/list');
const {
    addPortfolioItem,
    updatePortfolioItem,
    deletePortfolioItem,
    getPortfolioItems,
} = require('../controllers/contractor/portfolio');
const {
    userSignupValidators,
    userLoginValidators,
    refreshTokenValidator,
    updateProfileValidators,
    brokerApplicationValidators,
    contractorApplicationValidators,
    contractorUpdateValidators,
    brokerUpdateValidators,
    addPortfolioItemValidators,
    updatePortfolioItemValidators,
    deletePortfolioItemValidators,
} = require('../utils/validators');

const { Cloudinary } = require('../cloudinary');

const upload = multer({ storage: Cloudinary.getStorage() });

const doAuthenticate = createAuthMiddleware.NewAuthenticateMiddleware(client);

router.get('/', doAuthenticate, (req, res) => {
    const { user } = req;
    res.status(200).json(user);
});

router.post(
    '/login',
    userLoginValidators,
    handleValidationErrors,
    catchAsync(authControllers.login)
);

router.post(
    '/signup',
    userSignupValidators,
    handleValidationErrors,
    upload.single('avatar'),
    catchAsync(authControllers.signup)
);

router.post(
    '/refresh',
    refreshTokenValidator,
    handleValidationErrors,
    catchAsync(authControllers.refreshToken)
);

// User profile routes
router.get(
    '/user/profile',
    doAuthenticate,
    catchAsync(profileControllers.getUserProfile)
);
router.put(
    '/user/profile',
    doAuthenticate,
    upload.single('avatar'),
    updateProfileValidators,
    handleValidationErrors,
    catchAsync(profileControllers.updateUserProfile)
);

// Broker application route
router.post(
    '/brokers',
    doAuthenticate,
    upload.fields([
        { name: 'aadhaarDocument', maxCount: 1 },
        { name: 'panDocument', maxCount: 1 },
    ]),
    brokerApplicationValidators,
    handleValidationErrors,
    catchAsync(createBrokerApplication)
);

// contractor application route
router.post(
    '/contractors',
    doAuthenticate,
    upload.fields([
        { name: 'aadhaarDocument', maxCount: 1 },
        { name: 'panDocument', maxCount: 1 },
    ]),
    contractorApplicationValidators,
    handleValidationErrors,
    catchAsync(createContractorApplication)
);

// Update Broker Application
router.patch(
    '/brokers/:brokerId',
    doAuthenticate,
    upload.fields([
        { name: 'aadhaarDocument', maxCount: 1 },
        { name: 'panDocument', maxCount: 1 },
    ]),
    brokerUpdateValidators,
    handleValidationErrors,
    catchAsync(updateBrokerApplication)
);

// Updat Contractor Application
router.patch(
    '/contractors/:contractorId',
    doAuthenticate,
    upload.fields([
        { name: 'aadhaarDocument', maxCount: 1 },
        { name: 'panDocument', maxCount: 1 },
    ]),
    contractorUpdateValidators,
    handleValidationErrors,
    catchAsync(updateContractorApplication)
);

router.get(
    '/brokers',
    doAuthenticate,
    catchAsync(getBrokerApplicationByUserId)
);

router.get(
    '/contractors',
    doAuthenticate,
    catchAsync(getContractorApplicationByUserId)
);

// Get all brokers for home page cards
router.get('/brokers/all', doAuthenticate, catchAsync(getAllBrokers));

// Get all contractors for home page cards
router.get('/contractors/all', doAuthenticate, catchAsync(getAllContractors));

// Get individual broker profile
router.get(
    '/brokers/profile/:brokerId',
    doAuthenticate,
    catchAsync(getBrokerProfile)
);

// Get individual contractor profile
router.get(
    '/contractors/profile/:contractorId',
    doAuthenticate,
    catchAsync(getContractorProfile)
);

// Portfolio management routes for contractors
router.post(
    '/contractors/portfolio',
    doAuthenticate,
    upload.single('portfolioImage'),
    addPortfolioItemValidators,
    handleValidationErrors,
    catchAsync(addPortfolioItem)
);

router.patch(
    '/contractors/portfolio/:portfolioItemId',
    doAuthenticate,
    upload.single('portfolioImage'),
    updatePortfolioItemValidators,
    handleValidationErrors,
    catchAsync(updatePortfolioItem)
);

router.delete(
    '/contractors/portfolio/:portfolioItemId',
    doAuthenticate,
    deletePortfolioItemValidators,
    handleValidationErrors,
    catchAsync(deletePortfolioItem)
);

router.get(
    '/contractors/:contractorId/portfolio',
    doAuthenticate,
    catchAsync(getPortfolioItems)
);

// Professional listing endpoints for project service requests
router.get('/professionals/brokers', doAuthenticate, catchAsync(getBrokers));
router.get(
    '/professionals/contractors',
    doAuthenticate,
    catchAsync(getContractors)
);

module.exports = router;
