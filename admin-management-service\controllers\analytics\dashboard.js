const VerificationRequests = require('../../model/verificationRequests');
const ExpressError = require('@build-connect/utils/ExpressError');

/**
 * Get comprehensive dashboard analytics
 */
exports.getDashboardAnalytics = async (req, res) => {
    const { timeframe = '30d' } = req.query;
    
    try {
        // Get analytics from all services
        const [
            verificationStats,
            userStats,
            siteStats,
            transactionStats,
            ratingStats,
            notificationStats,
            supportStats
        ] = await Promise.allSettled([
            getVerificationAnalytics(timeframe),
            getUserAnalytics(req.headers, timeframe),
            getSiteAnalytics(req.headers, timeframe),
            getTransactionAnalytics(req.headers, timeframe),
            getRatingAnalytics(req.headers, timeframe),
            getNotificationAnalytics(req.headers, timeframe),
            getSupportAnalytics(req.headers, timeframe)
        ]);

        const analytics = {
            timeframe,
            overview: {
                totalUsers: userStats.status === 'fulfilled' ? userStats.value.totalUsers : 0,
                totalSites: siteStats.status === 'fulfilled' ? siteStats.value.totalSites : 0,
                totalTransactions: transactionStats.status === 'fulfilled' ? transactionStats.value.totalTransactions : 0,
                pendingVerifications: verificationStats.status === 'fulfilled' ? verificationStats.value.pending : 0,
                averageRating: ratingStats.status === 'fulfilled' ? ratingStats.value.averageRating : 0,
                activeSupports: supportStats.status === 'fulfilled' ? supportStats.value.activeTickets : 0
            },
            verifications: verificationStats.status === 'fulfilled' ? verificationStats.value : getDefaultVerificationStats(),
            users: userStats.status === 'fulfilled' ? userStats.value : getDefaultUserStats(),
            sites: siteStats.status === 'fulfilled' ? siteStats.value : getDefaultSiteStats(),
            transactions: transactionStats.status === 'fulfilled' ? transactionStats.value : getDefaultTransactionStats(),
            ratings: ratingStats.status === 'fulfilled' ? ratingStats.value : getDefaultRatingStats(),
            notifications: notificationStats.status === 'fulfilled' ? notificationStats.value : getDefaultNotificationStats(),
            support: supportStats.status === 'fulfilled' ? supportStats.value : getDefaultSupportStats(),
            serviceStatus: {
                userService: userStats.status === 'fulfilled',
                siteService: siteStats.status === 'fulfilled',
                paymentService: transactionStats.status === 'fulfilled',
                ratingService: ratingStats.status === 'fulfilled',
                notificationService: notificationStats.status === 'fulfilled',
                supportService: supportStats.status === 'fulfilled'
            }
        };

        res.status(200).json(analytics);
    } catch (error) {
        throw new ExpressError('Failed to fetch dashboard analytics', 500);
    }
};

/**
 * Get verification analytics from local database
 */
async function getVerificationAnalytics(timeframe) {
    let dateFilter = {};
    const now = new Date();
    
    switch (timeframe) {
        case '7d':
            dateFilter = { createdAt: { $gte: new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000) } };
            break;
        case '30d':
            dateFilter = { createdAt: { $gte: new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000) } };
            break;
        case '90d':
            dateFilter = { createdAt: { $gte: new Date(now.getTime() - 90 * 24 * 60 * 60 * 1000) } };
            break;
    }

    const stats = await VerificationRequests.aggregate([
        { $match: dateFilter },
        {
            $group: {
                _id: null,
                total: { $sum: 1 },
                pending: { $sum: { $cond: [{ $eq: ['$status', 'pending'] }, 1, 0] } },
                approved: { $sum: { $cond: [{ $eq: ['$status', 'approved'] }, 1, 0] } },
                rejected: { $sum: { $cond: [{ $eq: ['$status', 'rejected'] }, 1, 0] } },
                contractors: { $sum: { $cond: [{ $eq: ['$type', 'contractor'] }, 1, 0] } },
                brokers: { $sum: { $cond: [{ $eq: ['$type', 'broker'] }, 1, 0] } },
                sites: { $sum: { $cond: [{ $eq: ['$type', 'site'] }, 1, 0] } },
                highPriority: { $sum: { $cond: [{ $eq: ['$priority', 'high'] }, 1, 0] } }
            }
        }
    ]);

    const result = stats[0] || getDefaultVerificationStats();
    const processedCount = result.approved + result.rejected;
    const approvalRate = processedCount > 0 ? ((result.approved / processedCount) * 100).toFixed(2) : 0;

    return {
        ...result,
        approvalRate: parseFloat(approvalRate),
        processedCount
    };
}

/**
 * Get user analytics from user service
 */
async function getUserAnalytics(headers, timeframe) {
    const response = await fetch(`http://localhost:3007/user-service/api/v1/admin/analytics?timeframe=${timeframe}`, {
        method: 'GET',
        headers: {
            'Authorization': headers.authorization,
            'Session': headers.session,
            'Content-Type': 'application/json'
        }
    });

    if (!response.ok) {
        throw new Error('User service unavailable');
    }

    return await response.json();
}

/**
 * Get site analytics from site service
 */
async function getSiteAnalytics(headers, timeframe) {
    const response = await fetch(`http://localhost:3006/site-service/api/v1/admin/analytics?timeframe=${timeframe}`, {
        method: 'GET',
        headers: {
            'Authorization': headers.authorization,
            'Session': headers.session,
            'Content-Type': 'application/json'
        }
    });

    if (!response.ok) {
        throw new Error('Site service unavailable');
    }

    return await response.json();
}

/**
 * Get transaction analytics from payment service
 */
async function getTransactionAnalytics(headers, timeframe) {
    const response = await fetch(`http://localhost:3002/payment-service/api/v1/admin/analytics?timeframe=${timeframe}`, {
        method: 'GET',
        headers: {
            'Authorization': headers.authorization,
            'Session': headers.session,
            'Content-Type': 'application/json'
        }
    });

    if (!response.ok) {
        throw new Error('Payment service unavailable');
    }

    return await response.json();
}

/**
 * Get rating analytics from rating service
 */
async function getRatingAnalytics(headers, timeframe) {
    const response = await fetch(`http://localhost:3003/rating-service/api/v1/admin/analytics?timeframe=${timeframe}`, {
        method: 'GET',
        headers: {
            'Authorization': headers.authorization,
            'Session': headers.session,
            'Content-Type': 'application/json'
        }
    });

    if (!response.ok) {
        throw new Error('Rating service unavailable');
    }

    return await response.json();
}

/**
 * Get notification analytics from notification service
 */
async function getNotificationAnalytics(headers, timeframe) {
    const response = await fetch(`http://localhost:3004/notification-service/api/v1/admin/analytics?timeframe=${timeframe}`, {
        method: 'GET',
        headers: {
            'Authorization': headers.authorization,
            'Session': headers.session,
            'Content-Type': 'application/json'
        }
    });

    if (!response.ok) {
        throw new Error('Notification service unavailable');
    }

    return await response.json();
}

/**
 * Get support analytics from customer support service
 */
async function getSupportAnalytics(headers, timeframe) {
    const response = await fetch(`http://localhost:3005/customer-support-service/api/v1/admin/analytics?timeframe=${timeframe}`, {
        method: 'GET',
        headers: {
            'Authorization': headers.authorization,
            'Session': headers.session,
            'Content-Type': 'application/json'
        }
    });

    if (!response.ok) {
        throw new Error('Support service unavailable');
    }

    return await response.json();
}

// Default stats functions
function getDefaultVerificationStats() {
    return {
        total: 0,
        pending: 0,
        approved: 0,
        rejected: 0,
        contractors: 0,
        brokers: 0,
        sites: 0,
        highPriority: 0,
        approvalRate: 0,
        processedCount: 0
    };
}

function getDefaultUserStats() {
    return {
        totalUsers: 0,
        activeUsers: 0,
        newUsers: 0,
        contractors: 0,
        brokers: 0,
        verifiedUsers: 0
    };
}

function getDefaultSiteStats() {
    return {
        totalSites: 0,
        approvedSites: 0,
        pendingSites: 0,
        rejectedSites: 0,
        averagePrice: 0,
        totalValue: 0
    };
}

function getDefaultTransactionStats() {
    return {
        totalTransactions: 0,
        totalAmount: 0,
        successfulTransactions: 0,
        failedTransactions: 0,
        averageAmount: 0
    };
}

function getDefaultRatingStats() {
    return {
        totalRatings: 0,
        averageRating: 0,
        fiveStarRatings: 0,
        oneStarRatings: 0
    };
}

function getDefaultNotificationStats() {
    return {
        totalNotifications: 0,
        sentNotifications: 0,
        deliveredNotifications: 0,
        failedNotifications: 0
    };
}

function getDefaultSupportStats() {
    return {
        totalTickets: 0,
        openTickets: 0,
        closedTickets: 0,
        activeTickets: 0,
        averageResolutionTime: 0
    };
}
