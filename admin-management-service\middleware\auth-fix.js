const { getDecodedToken } = require('@build-connect/utils');

/**
 * Fixed Authentication Middleware for Admin Management Service
 * Addresses the "Cannot read properties of null (reading 'accessToken')" error
 */
function createFixedAuthMiddleware(redisClient) {
    return async (req, res, next) => {
        try {
            // Extract session ID from headers
            const sessionId = req.get('Session');
            if (!sessionId) {
                return res.status(401).json({ message: 'Not Authenticated' });
            }

            // Extract authorization header
            const authHeader = req.get('Authorization');
            if (!authHeader || !authHeader.startsWith('Bearer ')) {
                return res.status(401).json({ message: 'Not Authenticated' });
            }

            // Get session data from Redis
            const sessionData = await redisClient.get(sessionId);
            if (!sessionData) {
                return res.status(401).json({ message: 'Session expired or invalid' });
            }

            let parsedSessionData;
            try {
                parsedSessionData = JSON.parse(sessionData);
            } catch (parseError) {
                console.error('Failed to parse session data:', parseError);
                return res.status(401).json({ message: 'Invalid session data' });
            }

            // Check if session data has required properties
            if (!parsedSessionData || typeof parsedSessionData !== 'object') {
                return res.status(401).json({ message: 'Invalid session structure' });
            }

            // Validate access token exists in session
            if (!parsedSessionData.accessToken) {
                return res.status(401).json({ message: 'Access token not found in session' });
            }

            // Validate the access token
            try {
                const decodedToken = getDecodedToken(parsedSessionData.accessToken);
                
                // Check if token is expired
                const currentTime = Math.floor(Date.now() / 1000);
                if (decodedToken.exp && decodedToken.exp < currentTime) {
                    return res.status(401).json({ message: 'Access token expired' });
                }

                // Decode and attach user information if available
                if (parsedSessionData.idToken) {
                    try {
                        const decodedIdToken = Buffer.from(parsedSessionData.idToken, 'base64').toString('utf-8');
                        const userInfo = JSON.parse(decodedIdToken);
                        req.user = userInfo;
                    } catch (idTokenError) {
                        console.warn('Failed to decode ID token:', idTokenError.message);
                        // Continue without user info - some endpoints might not need it
                    }
                }

                // Attach session info to request
                req.sessionId = sessionId;
                req.accessToken = parsedSessionData.accessToken;
                
                next();
            } catch (tokenError) {
                console.error('Token validation failed:', tokenError);
                return res.status(401).json({ message: 'Invalid access token' });
            }

        } catch (error) {
            console.error('Authentication middleware error:', error);
            return res.status(500).json({ message: 'Authentication service error' });
        }
    };
}

module.exports = createFixedAuthMiddleware;
