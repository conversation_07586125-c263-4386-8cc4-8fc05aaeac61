Stack trace:
Frame         Function      Args
0007FFFFBF80  00021005FE8E (000210285F68, 00021026AB6E, 000000000000, 0007FFFFAE80) msys-2.0.dll+0x1FE8E
0007FFFFBF80  0002100467F9 (000000000000, 000000000000, 000000000000, 0007FFFFC258) msys-2.0.dll+0x67F9
0007FFFFBF80  000210046832 (000210286019, 0007FFFFBE38, 000000000000, 000000000000) msys-2.0.dll+0x6832
0007FFFFBF80  000210068CF6 (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28CF6
0007FFFFBF80  000210068E24 (0007FFFFBF90, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28E24
0007FFFFC260  00021006A225 (0007FFFFBF90, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A225
End of stack trace
Loaded modules:
000100400000 bash.exe
7FFACA100000 ntdll.dll
7FFAC9AB0000 KERNEL32.DLL
7FFAC7230000 KERNELBASE.dll
7FFAC9E30000 USER32.dll
7FFAC7A30000 win32u.dll
7FFAC9D70000 GDI32.dll
7FFAC7840000 gdi32full.dll
7FFAC7980000 msvcp_win.dll
7FFAC7BC0000 ucrtbase.dll
000210040000 msys-2.0.dll
7FFAC98E0000 advapi32.dll
7FFAC81C0000 msvcrt.dll
7FFAC7E90000 sechost.dll
7FFAC9100000 RPCRT4.dll
7FFAC6200000 CRYPTBASE.DLL
7FFAC7B20000 bcryptPrimitives.dll
7FFACA000000 IMM32.DLL
