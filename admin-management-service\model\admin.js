const mongoose = require('mongoose');

const adminSchema = new mongoose.Schema(
    {
        name: {
            type: String,
            required: true,
        },
        email: {
            type: String,
            required: true,
            unique: true,
        },
        password: {
            type: String,
            required: true,
        },
        role: {
            type: String,
            enum: ['super_admin', 'admin', 'moderator', 'support'],
            default: 'admin',
        },
        permissions: {
            type: [String],
            default: [],
            enum: [
                'user_management',
                'broker_management',
                'contractor_management',
                'land_management',
                'transaction_management',
                'support_management',
                'content_moderation',
                'system_settings',
                'analytics_view',
                'audit_logs',
                'admin_management'
            ]
        },
        isActive: {
            type: Boolean,
            default: true,
        },
        lastLogin: {
            type: Date,
        },
        createdBy: {
            type: mongoose.Schema.Types.ObjectId,
            ref: 'Admin',
        },
    },
    {
        timestamps: true,
    }
);

const Admin = mongoose.model('Admin', adminSchema);
module.exports = Admin;
