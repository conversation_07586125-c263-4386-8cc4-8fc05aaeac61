const express = require('express');

const router = express.Router();
const { catchAsync } = require('@build-connect/utils');
const createAuthMiddleware = require('@build-connect/utils/middleware/authmiddleware');

// Import controllers
const { getTickets } = require('../controllers/getadmins');
const {
    getVerificationRequests,
    getVerificationRequest,
} = require('../controllers/verification/list');

// Dashboard Analytics
const {
    getDashboardStats,
    getRevenueAnalytics,
    getUserGrowthAnalytics,
    getTransactionAnalytics,
} = require('../controllers/dashboard/analytics');

// User Management
const {
    getAllUsers,
    getUserDetails,
    updateUserStatus,
    verifyUser,
    deleteUser,
} = require('../controllers/users/userManagement');

// Broker Management
const {
    getAllBrokers,
    getBrokerApplications,
    approveBrokerApplication,
    rejectBrokerApplication,
    updateBrokerCommission,
} = require('../controllers/brokers/brokerManagement');

// Contractor Management
const {
    getAllContractors,
    getContractorApplications,
    approveContractorApplication,
    rejectContractorApplication,
    updateContractorRating,
} = require('../controllers/contractors/contractorManagement');

// Land Management
const {
    getAllLands,
    getLandVerificationQueue,
    verifyLandListing,
    rejectLandListing,
    flagLandListing,
} = require('../controllers/lands/landManagement');

// Transaction Management
const {
    getAllTransactions,
    getTransactionDetails,
    refundTransaction,
    disputeTransaction,
} = require('../controllers/transactions/transactionManagement');

// Support Ticket Management
const {
    getAllTickets,
    getTicketDetails,
    updateTicketStatus,
    assignTicket,
    addTicketResponse,
} = require('../controllers/support/ticketManagement');

// Content Moderation
const {
    getReportedContent,
    moderateContent,
    banContent,
} = require('../controllers/content/contentModeration');

// System Settings and Notifications
const {
    getSystemSettings,
    updateSystemSettings,
    getCommissionSettings,
    updateCommissionSettings,
    sendBulkNotification,
    getNotificationTemplates,
    createNotificationTemplate,
    updateNotificationTemplate,
} = require('../controllers/settings/systemSettings');

// Audit Logs and Reports
const {
    getAuditLogs,
    exportAuditLogs,
    generateReport,
    getReportHistory,
    downloadReport,
    getAdminUsers,
    createAdminUser,
    updateAdminUser,
    updateAdminPermissions,
    deactivateAdminUser,
} = require('../controllers/audit/auditLogs');

const { client } = require('../cache');

const doAuthenticate = createAuthMiddleware.NewAuthenticateMiddleware(client);

// Legacy routes
router.get('/', doAuthenticate, catchAsync(getTickets));

// Verification routes
router.get(
    '/verifications',
    doAuthenticate,
    catchAsync(getVerificationRequests)
);
router.get(
    '/verifications/:id',
    doAuthenticate,
    catchAsync(getVerificationRequest)
);

// Dashboard Analytics routes
router.get('/dashboard/stats', doAuthenticate, catchAsync(getDashboardStats));
router.get(
    '/dashboard/revenue',
    doAuthenticate,
    catchAsync(getRevenueAnalytics)
);
router.get(
    '/dashboard/user-growth',
    doAuthenticate,
    catchAsync(getUserGrowthAnalytics)
);
router.get(
    '/dashboard/transactions',
    doAuthenticate,
    catchAsync(getTransactionAnalytics)
);

// User Management routes
router.get('/users', doAuthenticate, catchAsync(getAllUsers));
router.get('/users/:userId', doAuthenticate, catchAsync(getUserDetails));
router.put(
    '/users/:userId/status',
    doAuthenticate,
    catchAsync(updateUserStatus)
);
router.put('/users/:userId/verify', doAuthenticate, catchAsync(verifyUser));
router.delete('/users/:userId', doAuthenticate, catchAsync(deleteUser));

// Broker Management routes
router.get('/brokers', doAuthenticate, catchAsync(getAllBrokers));
router.get(
    '/brokers/applications',
    doAuthenticate,
    catchAsync(getBrokerApplications)
);
router.post(
    '/brokers/applications/:applicationId/approve',
    doAuthenticate,
    catchAsync(approveBrokerApplication)
);
router.post(
    '/brokers/applications/:applicationId/reject',
    doAuthenticate,
    catchAsync(rejectBrokerApplication)
);
router.put(
    '/brokers/:brokerId/commission',
    doAuthenticate,
    catchAsync(updateBrokerCommission)
);

// Contractor Management routes
router.get('/contractors', doAuthenticate, catchAsync(getAllContractors));
router.get(
    '/contractors/applications',
    doAuthenticate,
    catchAsync(getContractorApplications)
);
router.post(
    '/contractors/applications/:applicationId/approve',
    doAuthenticate,
    catchAsync(approveContractorApplication)
);
router.post(
    '/contractors/applications/:applicationId/reject',
    doAuthenticate,
    catchAsync(rejectContractorApplication)
);
router.put(
    '/contractors/:contractorId/rating',
    doAuthenticate,
    catchAsync(updateContractorRating)
);

// Land Management routes
router.get('/lands', doAuthenticate, catchAsync(getAllLands));
router.get(
    '/lands/verification-queue',
    doAuthenticate,
    catchAsync(getLandVerificationQueue)
);
router.post(
    '/lands/:landId/verify',
    doAuthenticate,
    catchAsync(verifyLandListing)
);
router.post(
    '/lands/:landId/reject',
    doAuthenticate,
    catchAsync(rejectLandListing)
);
router.post('/lands/:landId/flag', doAuthenticate, catchAsync(flagLandListing));

// Transaction Management routes
router.get('/transactions', doAuthenticate, catchAsync(getAllTransactions));
router.get(
    '/transactions/:transactionId',
    doAuthenticate,
    catchAsync(getTransactionDetails)
);
router.post(
    '/transactions/:transactionId/refund',
    doAuthenticate,
    catchAsync(refundTransaction)
);
router.post(
    '/transactions/:transactionId/dispute',
    doAuthenticate,
    catchAsync(disputeTransaction)
);

// Support Ticket Management routes
router.get('/tickets', doAuthenticate, catchAsync(getAllTickets));
router.get('/tickets/:ticketId', doAuthenticate, catchAsync(getTicketDetails));
router.put(
    '/tickets/:ticketId/status',
    doAuthenticate,
    catchAsync(updateTicketStatus)
);
router.put(
    '/tickets/:ticketId/assign',
    doAuthenticate,
    catchAsync(assignTicket)
);
router.post(
    '/tickets/:ticketId/responses',
    doAuthenticate,
    catchAsync(addTicketResponse)
);

// Content Moderation routes
router.get('/content/reported', doAuthenticate, catchAsync(getReportedContent));
router.post('/content/moderate', doAuthenticate, catchAsync(moderateContent));
router.post('/content/ban', doAuthenticate, catchAsync(banContent));

// System Settings routes
router.get('/settings', doAuthenticate, catchAsync(getSystemSettings));
router.put('/settings', doAuthenticate, catchAsync(updateSystemSettings));
router.get(
    '/settings/commission',
    doAuthenticate,
    catchAsync(getCommissionSettings)
);
router.put(
    '/settings/commission',
    doAuthenticate,
    catchAsync(updateCommissionSettings)
);

// Notification routes
router.post(
    '/notifications/bulk',
    doAuthenticate,
    catchAsync(sendBulkNotification)
);
router.get(
    '/notifications/templates',
    doAuthenticate,
    catchAsync(getNotificationTemplates)
);
router.post(
    '/notifications/templates',
    doAuthenticate,
    catchAsync(createNotificationTemplate)
);
router.put(
    '/notifications/templates/:templateId',
    doAuthenticate,
    catchAsync(updateNotificationTemplate)
);

// Audit Logs and Reports routes
router.get('/audit-logs', doAuthenticate, catchAsync(getAuditLogs));
router.get('/audit-logs/export', doAuthenticate, catchAsync(exportAuditLogs));
router.post('/reports/generate', doAuthenticate, catchAsync(generateReport));
router.get('/reports/history', doAuthenticate, catchAsync(getReportHistory));
router.get(
    '/reports/:reportId/download',
    doAuthenticate,
    catchAsync(downloadReport)
);

// Admin User Management routes
router.get('/admin-users', doAuthenticate, catchAsync(getAdminUsers));
router.post('/admin-users', doAuthenticate, catchAsync(createAdminUser));
router.put(
    '/admin-users/:adminId',
    doAuthenticate,
    catchAsync(updateAdminUser)
);
router.put(
    '/admin-users/:adminId/permissions',
    doAuthenticate,
    catchAsync(updateAdminPermissions)
);
router.put(
    '/admin-users/:adminId/deactivate',
    doAuthenticate,
    catchAsync(deactivateAdminUser)
);

module.exports = router;
